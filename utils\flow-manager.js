// Venus-Millware AutoFill - Flow Management Utility
// Enhanced flow management with local file storage and validation
// Developer: Atha Rizki Pangestu - IT Rebinmas (Delloyd Group)

class FlowManager {
    constructor() {
        this.currentFlow = null;
        this.flowStorage = new FlowStorage();
        this.flowValidator = new FlowValidator();
        this.eventTester = new EventTester();
        this.flowCache = new Map();
        
        // Define the single source flow file
        this.FLOW_FILE_PATH = 'flows/millware-login-automation.json';
        this.FLOW_FILE_NAME = 'millware-login-automation.json';
        
        this.init();
    }

    init() {
        console.log('🔧 Flow Manager initialized - Single Source Mode');
        this.setupEventListeners();
        this.loadFlowFromPrimarySource();
    }

    setupEventListeners() {
        // File operations
        document.getElementById('saveFlowToFile')?.addEventListener('click', () => this.saveFlowToFile());
        document.getElementById('loadFlowFromFile')?.addEventListener('click', () => this.loadFlowFromFile());
        document.getElementById('deleteFlowFile')?.addEventListener('click', () => this.deleteFlowFile());
        
        // Validation
        document.getElementById('validateFlow')?.addEventListener('click', () => this.validateCurrentFlow());
        document.getElementById('dryRunFlow')?.addEventListener('click', () => this.performDryRun());
        
        // Individual testing
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('test-single-event')) {
                const eventIndex = parseInt(e.target.dataset.eventIndex);
                this.testSingleEvent(eventIndex);
            }
            if (e.target.classList.contains('test-all-events')) {
                this.testAllEvents();
            }
        });
    }

    // ================== FILE OPERATIONS ==================

    async saveFlowToFile() {
        try {
            if (!this.currentFlow) {
                throw new Error('No flow to save');
            }

            console.log('💾 Saving flow to file...');
            
            const fileName = this.generateFileName(this.currentFlow.name || 'untitled-flow');
            const flowData = {
                ...this.currentFlow,
                metadata: {
                    ...this.currentFlow.metadata,
                    exportedAt: new Date().toISOString(),
                    version: '1.0.0',
                    exportedBy: 'Venus-Millware AutoFill'
                }
            };

            // Validate before saving
            const validation = await this.flowValidator.validate(flowData);
            if (!validation.isValid) {
                console.warn('⚠️ Flow has validation warnings:', validation.warnings);
                const proceed = confirm(`Flow has ${validation.warnings.length} warnings. Save anyway?`);
                if (!proceed) return;
            }

            const success = await this.flowStorage.saveToFile(flowData, fileName);
            
            if (success) {
                this.showNotification('✅ Flow saved successfully to Downloads folder', 'success');
                
                // Save to extension storage as backup
                await this.saveToStorage(flowData);
                
                console.log('✅ Flow saved:', fileName);
            } else {
                throw new Error('Failed to save flow to file');
            }

        } catch (error) {
            console.error('❌ Error saving flow to file:', error);
            this.showNotification(`Failed to save flow: ${error.message}`, 'error');
        }
    }

    async loadFlowFromFile() {
        try {
            console.log('📂 Loading flow from file...');
            
            const fileData = await this.flowStorage.loadFromFile();
            
            if (!fileData) {
                console.log('📂 No file selected');
                return;
            }

            // Validate loaded flow
            const validation = await this.flowValidator.validate(fileData);
            
            if (!validation.isValid) {
                throw new Error(`Invalid flow file: ${validation.errors.join(', ')}`);
            }

            if (validation.warnings.length > 0) {
                const proceed = confirm(`Flow loaded with ${validation.warnings.length} warnings. Continue?`);
                if (!proceed) return;
            }

            // Set as current flow
            this.currentFlow = fileData;
            
            // Save to extension storage
            await this.saveToStorage(fileData);
            
            // Update UI
            await this.updateFlowUI();
            
            this.showNotification('✅ Flow loaded successfully', 'success');
            console.log('✅ Flow loaded:', fileData.name);

        } catch (error) {
            console.error('❌ Error loading flow from file:', error);
            this.showNotification(`Failed to load flow: ${error.message}`, 'error');
        }
    }

    async deleteFlowFile() {
        try {
            if (!this.currentFlow) {
                throw new Error('No flow selected to delete');
            }

            const flowName = this.currentFlow.name || 'untitled-flow';
            const confirmed = confirm(`Are you sure you want to delete the flow "${flowName}"?\n\nThis will remove:\n- Flow from extension storage\n- JSON file from Downloads folder`);
            
            if (!confirmed) return;

            console.log('🗑️ Deleting flow...');

            // Delete from file system
            const fileName = this.generateFileName(flowName);
            const fileDeleted = await this.flowStorage.deleteFile(fileName);
            
            // Delete from extension storage
            await this.deleteFromStorage(this.currentFlow.id);
            
            // Clear current flow
            this.currentFlow = null;
            
            // Update UI
            await this.updateFlowUI();
            
            const message = fileDeleted ? 
                '✅ Flow deleted from both storage and Downloads folder' :
                '✅ Flow deleted from storage (file may not exist)';
                
            this.showNotification(message, 'success');
            console.log('✅ Flow deleted successfully');

        } catch (error) {
            console.error('❌ Error deleting flow:', error);
            this.showNotification(`Failed to delete flow: ${error.message}`, 'error');
        }
    }

    // ================== VALIDATION SYSTEM ==================

    async validateCurrentFlow() {
        try {
            if (!this.currentFlow) {
                throw new Error('No flow to validate');
            }

            console.log('🔍 Validating current flow...');
            
            const validation = await this.flowValidator.validate(this.currentFlow);
            const preflightCheck = await this.flowValidator.preflightCheck(this.currentFlow);
            
            // Show validation results
            this.showValidationResults(validation, preflightCheck);
            
            return validation;

        } catch (error) {
            console.error('❌ Error validating flow:', error);
            this.showNotification(`Validation failed: ${error.message}`, 'error');
            return { isValid: false, errors: [error.message] };
        }
    }

    async performDryRun() {
        try {
            if (!this.currentFlow) {
                throw new Error('No flow to test');
            }

            console.log('🧪 Performing dry run...');
            this.showNotification('🧪 Starting dry run...', 'info');
            
            const dryRunResult = await this.flowValidator.dryRun(this.currentFlow);
            
            // Show dry run results
            this.showDryRunResults(dryRunResult);
            
            return dryRunResult;

        } catch (error) {
            console.error('❌ Error performing dry run:', error);
            this.showNotification(`Dry run failed: ${error.message}`, 'error');
        }
    }

    // ================== INDIVIDUAL EVENT TESTING ==================

    async testSingleEvent(eventIndex) {
        try {
            if (!this.currentFlow || !this.currentFlow.flow_steps) {
                throw new Error('No flow or events to test');
            }

            const event = this.currentFlow.flow_steps[eventIndex];
            if (!event) {
                throw new Error(`Event at index ${eventIndex} not found`);
            }

            console.log(`🧪 Testing single event: ${event.name || event.type}`);
            this.showNotification(`🧪 Testing event: ${event.name || event.type}`, 'info');
            
            const result = await this.eventTester.testEvent(event, eventIndex);
            
            // Update UI with test result
            this.updateEventTestResult(eventIndex, result);
            
            const message = result.success ? 
                `✅ Event test passed: ${event.name || event.type}` :
                `❌ Event test failed: ${result.error}`;
                
            this.showNotification(message, result.success ? 'success' : 'error');
            
            return result;

        } catch (error) {
            console.error('❌ Error testing single event:', error);
            this.showNotification(`Event test failed: ${error.message}`, 'error');
        }
    }

    async testAllEvents() {
        try {
            if (!this.currentFlow || !this.currentFlow.flow_steps) {
                throw new Error('No flow or events to test');
            }

            console.log('🧪 Testing all events...');
            this.showNotification('🧪 Starting comprehensive event testing...', 'info');
            
            const results = await this.eventTester.testAllEvents(this.currentFlow.flow_steps);
            
            // Show comprehensive test results
            this.showAllEventsTestResults(results);
            
            const passedCount = results.filter(r => r.success).length;
            const totalCount = results.length;
            const message = `🧪 Event testing completed: ${passedCount}/${totalCount} passed`;
            
            this.showNotification(message, passedCount === totalCount ? 'success' : 'warning');
            
            return results;

        } catch (error) {
            console.error('❌ Error testing all events:', error);
            this.showNotification(`Event testing failed: ${error.message}`, 'error');
        }
    }

    // ================== UTILITY METHODS ==================

    generateFileName(flowName) {
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const safeName = flowName.replace(/[^a-zA-Z0-9-_]/g, '-').toLowerCase();
        return `venus-flow-${safeName}-${timestamp}.json`;
    }

    async saveToStorage(flow) {
        const storageKey = `flow_${flow.id || Date.now()}`;
        await chrome.storage.local.set({ [storageKey]: flow });
    }

    async deleteFromStorage(flowId) {
        const storageKey = `flow_${flowId}`;
        await chrome.storage.local.remove(storageKey);
    }

    async updateFlowUI() {
        // Update flow list in popup
        const flowListContainer = document.getElementById('flowList');
        if (flowListContainer && this.currentFlow) {
            flowListContainer.innerHTML = this.generateFlowListHTML(this.currentFlow);
        }
    }

    generateFlowListHTML(flow) {
        if (!flow.flow_steps) return '<p>No events in this flow</p>';
        
        return flow.flow_steps.map((event, index) => {
            return `
                <div class="flow-event" data-index="${index}">
                    <div class="event-header">
                        <span class="event-type">${event.type}</span>
                        <span class="event-name">${event.name || 'Unnamed Event'}</span>
                        <div class="event-status" id="eventStatus_${index}">
                            <span class="status-indicator" data-status="pending">●</span>
                        </div>
                    </div>
                    <div class="event-actions">
                        <button class="btn btn-sm test-single-event" data-event-index="${index}">
                            🧪 Test Event
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="this.editEvent(${index})">
                            ✏️ Edit
                        </button>
                    </div>
                    <div class="event-description">
                        ${event.description || 'No description'}
                    </div>
                </div>
            `;
        }).join('') + `
            <div class="flow-global-actions">
                <button class="btn btn-primary test-all-events">
                    🧪 Test All Events
                </button>
                <button class="btn btn-secondary" id="validateFlow">
                    ✅ Validate Flow
                </button>
                <button class="btn btn-secondary" id="dryRunFlow">
                    🧪 Dry Run
                </button>
            </div>
        `;
    }

    updateEventTestResult(eventIndex, result) {
        const statusElement = document.getElementById(`eventStatus_${eventIndex}`);
        if (statusElement) {
            const indicator = statusElement.querySelector('.status-indicator');
            indicator.dataset.status = result.success ? 'success' : 'error';
            indicator.title = result.success ? 'Test passed' : `Test failed: ${result.error}`;
        }
    }

    showValidationResults(validation, preflightCheck) {
        const resultsHTML = `
            <div class="validation-results">
                <h4>Validation Results</h4>
                <div class="validation-status ${validation.isValid ? 'valid' : 'invalid'}">
                    ${validation.isValid ? '✅ Flow is valid' : '❌ Flow has errors'}
                </div>
                
                ${validation.errors.length > 0 ? `
                    <div class="validation-errors">
                        <h5>Errors:</h5>
                        <ul>${validation.errors.map(error => `<li>${error}</li>`).join('')}</ul>
                    </div>
                ` : ''}
                
                ${validation.warnings.length > 0 ? `
                    <div class="validation-warnings">
                        <h5>Warnings:</h5>
                        <ul>${validation.warnings.map(warning => `<li>${warning}</li>`).join('')}</ul>
                    </div>
                ` : ''}
                
                ${preflightCheck ? `
                    <div class="preflight-results">
                        <h5>Preflight Check:</h5>
                        <p>Elements found: ${preflightCheck.elementsFound}/${preflightCheck.totalElements}</p>
                        <p>Missing elements: ${preflightCheck.missingElements.join(', ') || 'None'}</p>
                    </div>
                ` : ''}
            </div>
        `;
        
        this.showModal('Flow Validation Results', resultsHTML);
    }

    showDryRunResults(dryRunResult) {
        const resultsHTML = `
            <div class="dry-run-results">
                <h4>Dry Run Results</h4>
                <div class="summary">
                    <p><strong>Total Events:</strong> ${dryRunResult.totalEvents}</p>
                    <p><strong>Simulated:</strong> ${dryRunResult.simulatedEvents}</p>
                    <p><strong>Skipped:</strong> ${dryRunResult.skippedEvents}</p>
                    <p><strong>Estimated Duration:</strong> ${dryRunResult.estimatedDuration}ms</p>
                </div>
                
                <div class="event-results">
                    <h5>Event Simulation Results:</h5>
                    ${dryRunResult.eventResults.map((result, index) => `
                        <div class="event-result ${result.status}">
                            <span class="event-name">${result.eventName}</span>
                            <span class="event-status">${result.status}</span>
                            ${result.notes ? `<div class="event-notes">${result.notes}</div>` : ''}
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
        
        this.showModal('Dry Run Results', resultsHTML);
    }

    showAllEventsTestResults(results) {
        const resultsHTML = `
            <div class="all-events-test-results">
                <h4>Event Testing Results</h4>
                <div class="summary">
                    <p><strong>Total Events:</strong> ${results.length}</p>
                    <p><strong>Passed:</strong> ${results.filter(r => r.success).length}</p>
                    <p><strong>Failed:</strong> ${results.filter(r => !r.success).length}</p>
                </div>
                
                <div class="detailed-results">
                    ${results.map((result, index) => `
                        <div class="test-result ${result.success ? 'success' : 'error'}">
                            <div class="result-header">
                                <span class="status-icon">${result.success ? '✅' : '❌'}</span>
                                <span class="event-name">${result.eventName}</span>
                                <span class="event-type">${result.eventType}</span>
                            </div>
                            ${!result.success ? `
                                <div class="error-details">
                                    <strong>Error:</strong> ${result.error}
                                </div>
                            ` : ''}
                            ${result.notes ? `
                                <div class="test-notes">
                                    <strong>Notes:</strong> ${result.notes}
                                </div>
                            ` : ''}
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
        
        this.showModal('All Events Test Results', resultsHTML);
    }

    showModal(title, content) {
        // Create and show modal dialog
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>${title}</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary modal-close">Close</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Close modal handlers
        modal.querySelectorAll('.modal-close').forEach(btn => {
            btn.addEventListener('click', () => {
                document.body.removeChild(modal);
            });
        });
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // Add to page
        document.body.appendChild(notification);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 5000);
        
        console.log(`[${type.toUpperCase()}] ${message}`);
    }

    /**
     * Load flow from the primary source file
     */
    async loadFlowFromPrimarySource() {
        try {
            console.log('📂 Loading flow from primary source:', this.FLOW_FILE_PATH);
            
            // Try to load from local file system first
            const flowData = await this.loadFlowFromExtensionFile();
            
            if (flowData) {
                this.currentFlow = flowData;
                await this.updateFlowUI();
                console.log('✅ Flow loaded from primary source');
            } else {
                console.warn('⚠️ Could not load from primary source');
            }
            
        } catch (error) {
            console.error('❌ Error loading primary flow:', error);
        }
    }

    /**
     * Load flow from extension file system
     */
    async loadFlowFromExtensionFile() {
        try {
            const response = await fetch(chrome.runtime.getURL(this.FLOW_FILE_PATH));
            if (response.ok) {
                const flowData = await response.json();
                return flowData;
            }
        } catch (error) {
            console.error('Error loading extension file:', error);
        }
        return null;
    }

    /**
     * Save flow back to primary source
     */
    async saveFlowToPrimarySource() {
        try {
            if (!this.currentFlow) {
                throw new Error('No flow to save');
            }

            console.log('💾 Saving flow to primary source...');
            
            // Update metadata
            this.currentFlow.metadata = {
                ...this.currentFlow.metadata,
                lastModified: new Date().toISOString(),
                modifiedBy: 'Venus-Millware AutoFill'
            };

            // Save to Downloads folder with the specific filename
            const success = await this.flowStorage.saveToFile(this.currentFlow, this.FLOW_FILE_NAME);
            
            if (success) {
                this.showNotification('✅ Flow saved to primary source', 'success');
                console.log('✅ Flow saved to primary source');
                return true;
            } else {
                throw new Error('Failed to save flow');
            }

        } catch (error) {
            console.error('❌ Error saving to primary source:', error);
            this.showNotification(`Failed to save: ${error.message}`, 'error');
            return false;
        }
    }

    // ================== EVENT CRUD OPERATIONS ==================

    /**
     * Add new event to flow
     */
    async addEvent(eventData, insertIndex = null) {
        try {
            if (!this.currentFlow) {
                throw new Error('No flow loaded');
            }

            // Generate new event ID
            const newEventId = this.generateEventId();
            
            // Create event with default structure
            const newEvent = {
                id: newEventId,
                name: eventData.name || 'New Event',
                type: eventData.type || 'wait',
                description: eventData.description || 'New event',
                order: insertIndex !== null ? insertIndex + 1 : this.currentFlow.flow_steps.length + 1,
                visual_node: {
                    icon: this.getEventIcon(eventData.type),
                    title: eventData.name || 'New Event',
                    subtitle: eventData.description || 'New event',
                    position: { x: 100, y: (insertIndex !== null ? insertIndex : this.currentFlow.flow_steps.length) * 70 + 50 }
                },
                parameters: eventData.parameters || {},
                success_criteria: eventData.success_criteria || {},
                error_handling: {
                    retry_count: 1,
                    fallback_action: 'continue'
                }
            };

            // Insert event at specified index or append
            if (insertIndex !== null) {
                this.currentFlow.flow_steps.splice(insertIndex, 0, newEvent);
                // Update order numbers for subsequent events
                this.reorderEvents();
            } else {
                this.currentFlow.flow_steps.push(newEvent);
            }

            // Update flow connections
            this.updateFlowConnections();

            // Save changes
            await this.saveFlowToPrimarySource();
            
            // Update UI
            await this.updateFlowUI();

            this.showNotification('✅ Event added successfully', 'success');
            console.log('✅ Event added:', newEvent.name);
            
            return newEvent;

        } catch (error) {
            console.error('❌ Error adding event:', error);
            this.showNotification(`Failed to add event: ${error.message}`, 'error');
            throw error;
        }
    }

    /**
     * Update existing event
     */
    async updateEvent(eventIndex, eventData) {
        try {
            if (!this.currentFlow || !this.currentFlow.flow_steps) {
                throw new Error('No flow loaded');
            }

            if (eventIndex < 0 || eventIndex >= this.currentFlow.flow_steps.length) {
                throw new Error('Invalid event index');
            }

            const existingEvent = this.currentFlow.flow_steps[eventIndex];
            
            // Merge updated data with existing event
            const updatedEvent = {
                ...existingEvent,
                ...eventData,
                id: existingEvent.id, // Preserve ID
                order: existingEvent.order, // Preserve order
                visual_node: {
                    ...existingEvent.visual_node,
                    ...(eventData.visual_node || {}),
                    title: eventData.name || existingEvent.name,
                    subtitle: eventData.description || existingEvent.description
                },
                parameters: {
                    ...existingEvent.parameters,
                    ...(eventData.parameters || {})
                },
                success_criteria: {
                    ...existingEvent.success_criteria,
                    ...(eventData.success_criteria || {})
                }
            };

            // Update the event in flow
            this.currentFlow.flow_steps[eventIndex] = updatedEvent;

            // Save changes
            await this.saveFlowToPrimarySource();
            
            // Update UI
            await this.updateFlowUI();

            this.showNotification('✅ Event updated successfully', 'success');
            console.log('✅ Event updated:', updatedEvent.name);
            
            return updatedEvent;

        } catch (error) {
            console.error('❌ Error updating event:', error);
            this.showNotification(`Failed to update event: ${error.message}`, 'error');
            throw error;
        }
    }

    /**
     * Delete event from flow
     */
    async deleteEvent(eventIndex) {
        try {
            if (!this.currentFlow || !this.currentFlow.flow_steps) {
                throw new Error('No flow loaded');
            }

            if (eventIndex < 0 || eventIndex >= this.currentFlow.flow_steps.length) {
                throw new Error('Invalid event index');
            }

            const eventToDelete = this.currentFlow.flow_steps[eventIndex];
            const confirmed = confirm(`Are you sure you want to delete event "${eventToDelete.name}"?`);
            
            if (!confirmed) return false;

            // Remove event from flow
            this.currentFlow.flow_steps.splice(eventIndex, 1);
            
            // Reorder remaining events
            this.reorderEvents();
            
            // Update flow connections
            this.updateFlowConnections();

            // Save changes
            await this.saveFlowToPrimarySource();
            
            // Update UI
            await this.updateFlowUI();

            this.showNotification('✅ Event deleted successfully', 'success');
            console.log('✅ Event deleted:', eventToDelete.name);
            
            return true;

        } catch (error) {
            console.error('❌ Error deleting event:', error);
            this.showNotification(`Failed to delete event: ${error.message}`, 'error');
            throw error;
        }
    }

    /**
     * Execute single event
     */
    async executeSingleEvent(eventIndex) {
        try {
            if (!this.currentFlow || !this.currentFlow.flow_steps) {
                throw new Error('No flow loaded');
            }

            const event = this.currentFlow.flow_steps[eventIndex];
            if (!event) {
                throw new Error(`Event at index ${eventIndex} not found`);
            }

            console.log(`🚀 Executing single event: ${event.name}`);
            this.showNotification(`🚀 Executing: ${event.name}`, 'info');
            
            // Get current tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            // Send execution message to content script
            const response = await chrome.tabs.sendMessage(tab.id, {
                action: 'executeSingleEvent',
                event: event,
                index: eventIndex
            });

            if (response && response.success) {
                this.showNotification(`✅ Event executed: ${event.name}`, 'success');
                console.log('✅ Event executed successfully:', event.name);
                return { success: true, result: response };
            } else {
                throw new Error(response?.error || 'Event execution failed');
            }

        } catch (error) {
            console.error('❌ Error executing single event:', error);
            this.showNotification(`Failed to execute event: ${error.message}`, 'error');
            return { success: false, error: error.message };
        }
    }

    /**
     * Generate unique event ID
     */
    generateEventId() {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substr(2, 5);
        return `step_${timestamp}_${random}`;
    }

    /**
     * Reorder events after insertion/deletion
     */
    reorderEvents() {
        if (!this.currentFlow || !this.currentFlow.flow_steps) return;
        
        this.currentFlow.flow_steps.forEach((event, index) => {
            event.order = index + 1;
            if (event.visual_node) {
                event.visual_node.position.y = index * 70 + 50;
            }
        });
    }

    /**
     * Update flow connections
     */
    updateFlowConnections() {
        if (!this.currentFlow || !this.currentFlow.flow_steps) return;
        
        const connections = [];
        for (let i = 0; i < this.currentFlow.flow_steps.length - 1; i++) {
            connections.push({
                from: this.currentFlow.flow_steps[i].id,
                to: this.currentFlow.flow_steps[i + 1].id
            });
        }
        
        this.currentFlow.flow_connections = connections;
    }

    /**
     * Get event icon based on type
     */
    getEventIcon(type) {
        const icons = {
            'navigate': '🌐',
            'wait': '⏳',
            'input': '⌨️',
            'click': '👆',
            'text_search_click': '🔍',
            'conditional_action': '🔀',
            'extract': '📊'
        };
        return icons[type] || '⚙️';
    }
}

// Flow Storage Handler
class FlowStorage {
    async saveToFile(flowData, fileName) {
        try {
            // Convert flow data to JSON blob
            const jsonData = JSON.stringify(flowData, null, 2);
            const blob = new Blob([jsonData], { type: 'application/json' });
            
            // Create download URL
            const url = URL.createObjectURL(blob);
            
            // Trigger download using Chrome downloads API
            const downloadId = await chrome.downloads.download({
                url: url,
                filename: fileName,
                saveAs: false // Auto-save to Downloads folder
            });
            
            console.log(`💾 Flow saved with download ID: ${downloadId}`);
            
            // Clean up blob URL
            URL.revokeObjectURL(url);
            
            return true;

        } catch (error) {
            console.error('❌ Error saving to file:', error);
            return false;
        }
    }

    async loadFromFile() {
        try {
            // Use file picker
            const [fileHandle] = await window.showOpenFilePicker({
                types: [{
                    description: 'Venus Flow JSON files',
                    accept: {
                        'application/json': ['.json']
                    }
                }],
                multiple: false
            });

            const file = await fileHandle.getFile();
            const text = await file.text();
            
            // Parse JSON
            const flowData = JSON.parse(text);
            
            console.log('📂 Flow loaded from file:', file.name);
            return flowData;

        } catch (error) {
            if (error.name === 'AbortError') {
                console.log('📂 File selection cancelled');
                return null;
            }
            
            console.error('❌ Error loading from file:', error);
            throw error;
        }
    }

    async deleteFile(fileName) {
        try {
            // Search for the file in Downloads folder using Chrome downloads API
            const downloads = await chrome.downloads.search({
                filename: fileName,
                state: 'complete'
            });

            if (downloads.length > 0) {
                // Try to remove the file
                for (const download of downloads) {
                    try {
                        await chrome.downloads.removeFile(download.id);
                        console.log(`🗑️ Successfully deleted file: ${fileName}`);
                        return true;
                    } catch (error) {
                        console.warn(`⚠️ Could not delete file ${fileName}:`, error);
                        // Try to erase from downloads history at least
                        await chrome.downloads.erase({ id: download.id });
                        console.log(`🗑️ Removed from downloads history: ${fileName}`);
                        return true;
                    }
                }
            } else {
                // Search with pattern matching for similar files
                const pattern = fileName.replace(/\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2}/, '*');
                const patternDownloads = await chrome.downloads.search({
                    filenameRegex: pattern.replace(/\*/g, '.*'),
                    state: 'complete'
                });

                if (patternDownloads.length > 0) {
                    for (const download of patternDownloads) {
                        try {
                            await chrome.downloads.removeFile(download.id);
                            console.log(`🗑️ Successfully deleted similar file: ${download.filename}`);
                            return true;
                        } catch (error) {
                            await chrome.downloads.erase({ id: download.id });
                            console.log(`🗑️ Removed from downloads history: ${download.filename}`);
                            return true;
                        }
                    }
                }

                console.log(`ℹ️ File ${fileName} not found in Downloads folder`);
                return false;
            }

        } catch (error) {
            console.error('❌ Error deleting file:', error);
            return false;
        }
    }

    async loadFromFileWithFallback() {
        try {
            // Try File System Access API first (Chrome 86+)
            if ('showOpenFilePicker' in window) {
                return await this.loadFromFile();
            } else {
                // Fallback to input file method
                return await this.loadFromFileInput();
            }
        } catch (error) {
            console.error('❌ Error loading file:', error);
            throw error;
        }
    }

    async loadFromFileInput() {
        return new Promise((resolve, reject) => {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.style.display = 'none';

            input.onchange = async (event) => {
                try {
                    const file = event.target.files[0];
                    if (!file) {
                        reject(new Error('No file selected'));
                        return;
                    }

                    const text = await file.text();
                    const flowData = JSON.parse(text);

                    console.log('📂 Flow loaded from file input:', file.name);
                    resolve(flowData);

                } catch (error) {
                    reject(error);
                } finally {
                    document.body.removeChild(input);
                }
            };

            input.oncancel = () => {
                document.body.removeChild(input);
                reject(new Error('File selection cancelled'));
            };

            document.body.appendChild(input);
            input.click();
        });
    }
}

// Flow Validator
class FlowValidator {
    async validate(flow) {
        const errors = [];
        const warnings = [];

        // Basic structure validation
        if (!flow.flow_steps || !Array.isArray(flow.flow_steps)) {
            errors.push('Flow must have flow_steps array');
        }

        if (!flow.name) {
            warnings.push('Flow should have a name');
        }

        if (flow.flow_steps) {
            for (let i = 0; i < flow.flow_steps.length; i++) {
                const event = flow.flow_steps[i];
                const eventErrors = this.validateEvent(event, i);
                errors.push(...eventErrors);
            }
        }

        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }

    validateEvent(event, index) {
        const errors = [];

        if (!event.type) {
            errors.push(`Event ${index}: Missing event type`);
        }

        // Type-specific validation
        switch (event.type) {
            case 'click':
                if (!event.selector && !event.searchText) {
                    errors.push(`Event ${index}: Click event requires selector or searchText`);
                }
                break;
                
            case 'input':
                if (!event.selector) {
                    errors.push(`Event ${index}: Input event requires selector`);
                }
                if (!event.value && !event.dataMapping) {
                    errors.push(`Event ${index}: Input event requires value or dataMapping`);
                }
                break;
                
            case 'navigate':
                if (!event.url) {
                    errors.push(`Event ${index}: Navigate event requires URL`);
                }
                break;
        }

        return errors;
    }

    async preflightCheck(flow) {
        if (!flow.flow_steps) return null;

        let elementsFound = 0;
        let totalElements = 0;
        const missingElements = [];

        for (const event of flow.flow_steps) {
            if (event.selector) {
                totalElements++;
                
                // Check if element exists on current page
                const element = document.querySelector(event.selector);
                if (element) {
                    elementsFound++;
                } else {
                    missingElements.push(event.selector);
                }
            }
        }

        return {
            elementsFound,
            totalElements,
            missingElements
        };
    }

    async dryRun(flow) {
        const eventResults = [];
        let simulatedEvents = 0;
        let skippedEvents = 0;
        let estimatedDuration = 0;

        for (let i = 0; i < flow.flow_steps.length; i++) {
            const event = flow.flow_steps[i];
            
            const result = {
                eventName: event.name || `Event ${i + 1}`,
                eventType: event.type,
                status: 'simulated',
                notes: ''
            };

            // Estimate duration
            estimatedDuration += this.estimateEventDuration(event);

            // Simulate based on event type
            switch (event.type) {
                case 'click':
                    if (event.selector && document.querySelector(event.selector)) {
                        result.status = 'ready';
                        result.notes = 'Element found and ready for click';
                    } else {
                        result.status = 'warning';
                        result.notes = 'Element not found on current page';
                    }
                    simulatedEvents++;
                    break;
                    
                case 'wait':
                    result.status = 'ready';
                    result.notes = `Will wait ${event.duration || 1000}ms`;
                    simulatedEvents++;
                    break;
                    
                case 'navigate':
                    result.status = 'ready';
                    result.notes = `Will navigate to ${event.url}`;
                    simulatedEvents++;
                    break;
                    
                default:
                    result.status = 'skipped';
                    result.notes = 'Event type not simulated in dry run';
                    skippedEvents++;
            }

            eventResults.push(result);
        }

        return {
            totalEvents: flow.flow_steps.length,
            simulatedEvents,
            skippedEvents,
            estimatedDuration,
            eventResults
        };
    }

    estimateEventDuration(event) {
        switch (event.type) {
            case 'wait':
                return event.duration || 1000;
            case 'navigate':
                return 3000; // Estimated page load time
            case 'click':
            case 'input':
                return 500;
            default:
                return 1000;
        }
    }
}

// Event Tester
class EventTester {
    async testEvent(event, index) {
        try {
            console.log(`🧪 Testing event ${index}: ${event.type}`);
            
            // Send test message to content script
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            const response = await chrome.tabs.sendMessage(tab.id, {
                action: 'testEvent',
                event: event,
                index: index
            });

            return {
                success: response.success,
                error: response.error || null,
                eventName: event.name || `Event ${index + 1}`,
                eventType: event.type,
                notes: response.notes || null
            };

        } catch (error) {
            return {
                success: false,
                error: error.message,
                eventName: event.name || `Event ${index + 1}`,
                eventType: event.type
            };
        }
    }

    async testAllEvents(events) {
        const results = [];
        
        for (let i = 0; i < events.length; i++) {
            const result = await this.testEvent(events[i], i);
            results.push(result);
            
            // Small delay between tests
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        return results;
    }
}

// Export for use in popup
if (typeof window !== 'undefined') {
    window.FlowManager = FlowManager;
} 