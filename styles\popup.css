/* Chrome Extension Automation Bot - Popup Styles */

:root {
    --primary-color: #4caf50;
    --secondary-color: #2196f3;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --success-color: #4caf50;
    --background-color: #ffffff;
    --border-color: #e0e0e0;
    --text-color: #333333;
    --accent-color: #6c757d;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 14px;
    color: var(--text-color);
    background-color: var(--background-color);
    width: 500px;
    max-height: 700px;
    overflow-y: auto;
    min-height: 400px;
}

.container {
    padding: 0;
}

/* Header */
.header {
    background: linear-gradient(135deg, #4caf50, #2196f3);
    color: white;
    padding: 16px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    position: relative;
}

.logo {
    width: 32px;
    height: 32px;
    border-radius: 6px;
}

.header h1 {
    font-size: 18px;
    font-weight: 600;
    flex: 1;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    background: rgba(255, 255, 255, 0.15);
    padding: 4px 8px;
    border-radius: 12px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #4caf50;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Quick Run Section */
.quick-run-section {
    background: linear-gradient(135deg, #e8f5e8, #e3f2fd);
    border-bottom: 2px solid #4caf50;
    padding: 20px;
    margin: 0;
}

.quick-run-section h3 {
    font-size: 16px;
    font-weight: 600;
    color: #2e7d32;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.quick-run-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 15px;
}

.info-item {
    font-size: 12px;
    color: #555;
}

.info-item strong {
    color: #2e7d32;
    font-weight: 600;
}

.btn-primary-action {
    background: linear-gradient(135deg, #4caf50, #45a049) !important;
    border: none !important;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3) !important;
    font-weight: 600 !important;
    font-size: 16px !important;
    padding: 12px 24px !important;
    width: 100% !important;
    transition: all 0.3s ease !important;
}

.btn-primary-action:hover {
    background: linear-gradient(135deg, #45a049, #3d8b40) !important;
    box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4) !important;
    transform: translateY(-1px) !important;
}

/* Navigation Tabs */
.nav-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid var(--border-color);
    padding: 0;
}

.tab-button {
    flex: 1;
    padding: 12px 8px;
    border: none;
    background: transparent;
    font-size: 12px;
    font-weight: 500;
    color: var(--accent-color);
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 3px solid transparent;
}

.tab-button:hover {
    background: rgba(33, 150, 243, 0.1);
    color: var(--secondary-color);
}

.tab-button.active {
    color: var(--secondary-color);
    border-bottom-color: var(--secondary-color);
    background: white;
    font-weight: 600;
}

/* Tab Content */
.tab-content {
    display: none;
    padding: 20px;
    max-height: 500px;
    overflow-y: auto;
    overflow-x: hidden;
}

.tab-content.active {
    display: block;
}

/* Sections */
.section {
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.section h3 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Form Elements */
.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    font-size: 12px;
    font-weight: 500;
    margin-bottom: 6px;
    color: var(--text-color);
}

.form-group input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-group input:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.form-group small {
    display: block;
    font-size: 11px;
    color: var(--accent-color);
    margin-top: 4px;
}

/* Buttons */
.btn {
    padding: 8px 16px;
    border: 1px solid transparent;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-right: 8px;
    margin-bottom: 8px;
    display: inline-block;
    text-decoration: none;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: #45a049;
    border-color: #45a049;
}

.btn-secondary {
    background: #6c757d;
    color: white;
    border-color: #6c757d;
}

.btn-secondary:hover {
    background: #5a6268;
    border-color: #5a6268;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
    border-color: var(--warning-color);
}

.btn-warning:hover {
    background: #e68900;
    border-color: #e68900;
}

.btn-success {
    background: var(--success-color);
    color: white;
    border-color: var(--success-color);
}

.btn-success:hover {
    background: #45a049;
    border-color: #45a049;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
    border-color: var(--danger-color);
}

.btn-danger:hover {
    background: #d32f2f;
    border-color: #d32f2f;
}

.btn-large {
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
}

.btn-small {
    padding: 4px 8px;
    font-size: 11px;
}

/* Data Controls */
.data-controls, .flow-controls, .execution-controls {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
    flex-wrap: wrap;
}

/* Data Status */
.data-status {
    padding: 12px;
    background: #f8f9fa;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    margin-bottom: 16px;
    font-size: 12px;
}

/* Data Preview */
.data-preview {
    max-height: 300px;
    overflow: auto;
    border: 1px solid var(--border-color);
    border-radius: 6px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 11px;
}

.data-table th {
    background: #f8f9fa;
    padding: 8px 6px;
    text-align: left;
    font-weight: 600;
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
}

.data-table td {
    padding: 6px;
    border-bottom: 1px solid #f0f0f0;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.data-table tr:hover {
    background: #f8f9fa;
}

/* Flow Events */
.flow-list {
    max-height: 250px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: #fafafa;
}

.flow-event {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
    background: white;
    margin-bottom: 1px;
}

.flow-event:last-child {
    border-bottom: none;
}

.event-info {
    flex: 1;
}

.event-type {
    font-size: 11px;
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: 4px;
}

.event-details {
    font-size: 12px;
    color: var(--text-color);
    line-height: 1.3;
}

.event-actions {
    display: flex;
    gap: 4px;
}

/* Execution Status */
.execution-status {
    margin-bottom: 16px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--success-color), #66bb6a);
    width: 0%;
    transition: width 0.3s ease;
}

.execution-status .status-text {
    font-size: 12px;
    color: var(--accent-color);
}

/* Execution Log */
.execution-log {
    max-height: 200px;
    overflow-y: auto;
    background: #f8f9fa;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 12px;
    font-family: 'Consolas', monospace;
    font-size: 11px;
    line-height: 1.4;
}

.log-entry {
    margin-bottom: 4px;
    padding: 2px 0;
}

.log-timestamp {
    color: var(--accent-color);
    font-weight: 500;
}

.log-info {
    color: var(--secondary-color);
}

.log-success {
    color: var(--success-color);
}

.log-warning {
    color: var(--warning-color);
}

.log-error {
    color: var(--danger-color);
}

/* Debug Info */
.debug-info {
    background: #f8f9fa;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 16px;
    margin-top: 16px;
}

/* Flow Quick Actions */
.flow-quick-actions {
    text-align: center;
}

.flow-quick-actions h4 {
    color: var(--text-color);
    margin-bottom: 12px;
}

/* Responsive Design */
@media (max-width: 480px) {
    body {
        width: 100%;
        min-width: 450px;
    }
    .quick-run-info {
        grid-template-columns: 1fr;
    }
    .data-controls, .flow-controls, .execution-controls {
        flex-direction: column;
        gap: 8px;
    }
    .btn {
        min-width: 100px;
        font-size: 12px;
    }
}

/* Animation for success states */
.success-pulse {
    animation: successPulse 1s ease-in-out;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Text Search Styles */
.text-search-info {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 15px;
    border-left: 4px solid #007bff;
}

.search-options {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    align-items: center;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
}

.search-controls {
    display: flex;
    gap: 8px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.search-navigation {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    margin-bottom: 15px;
}

.search-results-info {
    font-weight: 500;
    margin-bottom: 10px;
    color: #495057;
    text-align: center;
}

.navigation-controls {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 4px;
}

.keyboard-shortcuts {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    margin-top: 15px;
}

.keyboard-shortcuts h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #495057;
}

.keyboard-shortcuts ul {
    margin: 0;
    padding-left: 20px;
    font-size: 12px;
    color: #6c757d;
}

.keyboard-shortcuts li {
    margin-bottom: 4px;
}

.keyboard-shortcuts strong {
    color: #495057;
    font-family: 'Courier New', monospace;
    background: #e9ecef;
    padding: 2px 4px;
    border-radius: 3px;
}

/* Post-Login Automation Button */
#runPostLoginAutomation {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
    color: white;
    margin-top: 8px;
    transition: all 0.3s ease;
}

#runPostLoginAutomation:hover {
    background: linear-gradient(135deg, #218838, #1ea085);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

#runPostLoginAutomation:active {
    transform: translateY(0);
}

/* Enhanced button styles for text search */
.search-controls .btn {
    min-width: 80px;
}

.search-controls .btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.search-controls .btn-secondary {
    background: linear-gradient(135deg, #6c757d, #545b62);
}

/* Enhanced Flow Management Styles */
.file-operations-section {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 4px solid #007bff;
}

.file-operations-section h4 {
    margin: 0 0 12px 0;
    color: #495057;
    font-size: 14px;
    font-weight: 600;
}

.file-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 10px;
}

.file-controls .btn {
    flex: 1;
    min-width: 120px;
    font-size: 12px;
    padding: 8px 12px;
}

.help-text {
    font-size: 11px;
    color: #666;
    margin-top: 5px;
    line-height: 1.3;
}

/* Validation Section */
.validation-section {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.validation-section h4 {
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #333;
}

.validation-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 10px;
}

.validation-controls .btn {
    flex: 1;
    min-width: 110px;
    font-size: 12px;
    padding: 8px 12px;
}

.validation-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
}

.status-indicator {
    font-size: 12px;
    font-weight: bold;
}

.status-indicator[data-status="pending"] {
    color: #6c757d;
}

.status-indicator[data-status="success"] {
    color: #28a745;
}

.status-indicator[data-status="error"] {
    color: #dc3545;
}

.status-indicator[data-status="testing"] {
    color: #ffc107;
}

.flow-list-container {
    margin-bottom: 20px;
}

.flow-list-container h4 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 16px;
    font-weight: 600;
}

.flow-event {
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 12px;
    padding: 15px;
    transition: all 0.2s ease;
}

.flow-event:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.event-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.event-info {
    flex: 1;
}

.event-type {
    font-weight: 600;
    color: #495057;
    font-size: 13px;
    margin-bottom: 4px;
}

.event-details {
    font-size: 12px;
    color: #6c757d;
    line-height: 1.4;
}

.event-actions {
    display: flex;
    gap: 6px;
    flex-shrink: 0;
}

.event-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    padding-top: 8px;
    border-top: 1px solid #f1f3f4;
}

.status-text {
    color: #6c757d;
}

.flow-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 4px solid #17a2b8;
}

.flow-info h4 {
    margin: 0 0 12px 0;
    color: #495057;
    font-size: 14px;
    font-weight: 600;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
}

.info-item {
    font-size: 12px;
}

.info-item strong {
    color: #495057;
    display: block;
    margin-bottom: 2px;
}

.no-flow-message {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px dashed #dee2e6;
}

/* Modal Styles for Results Display */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(3px);
    padding: 20px;
}

.modal-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.3);
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease;
    width: auto;
    min-width: 400px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #dee2e6;
}

.modal-header h3 {
    margin: 0;
    color: #495057;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #495057;
}

.modal-footer {
    padding: 15px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Validation Results Styles */
.validation-results .validation-status.valid {
    color: #28a745;
    font-weight: 600;
    margin-bottom: 15px;
}

.validation-results .validation-status.invalid {
    color: #dc3545;
    font-weight: 600;
    margin-bottom: 15px;
}

.validation-errors, .validation-warnings {
    margin-bottom: 15px;
}

.validation-errors h5 {
    color: #dc3545;
    margin: 0 0 8px 0;
    font-size: 14px;
}

.validation-warnings h5 {
    color: #ffc107;
    margin: 0 0 8px 0;
    font-size: 14px;
}

.validation-errors ul, .validation-warnings ul {
    margin: 0;
    padding-left: 20px;
    font-size: 13px;
}

/* Dry Run Results Styles */
.dry-run-results .summary {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 15px;
}

.dry-run-results .summary p {
    margin: 0 0 5px 0;
    font-size: 13px;
}

.event-result {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 10px;
    margin-bottom: 8px;
}

.event-result.simulated {
    border-left: 4px solid #28a745;
}

.event-result.skipped {
    border-left: 4px solid #ffc107;
}

.event-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.event-name {
    font-weight: 600;
    font-size: 13px;
}

.event-type {
    font-size: 11px;
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    color: #495057;
}

.event-status {
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 3px;
    text-transform: uppercase;
    font-weight: 600;
}

.event-result.simulated .event-status {
    background: #d4edda;
    color: #155724;
}

.event-result.skipped .event-status {
    background: #fff3cd;
    color: #856404;
}

.event-notes {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
}

/* Responsive Design for Enhanced Features */
@media (max-width: 400px) {
    .file-controls, .validation-controls {
        flex-direction: column;
    }

    .file-controls .btn, .validation-controls .btn {
        width: 100%;
        margin-bottom: 5px;
    }

    .event-actions {
        flex-direction: column;
        gap: 4px;
    }

    .event-actions .btn {
        font-size: 11px;
        padding: 4px 8px;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        margin: 20px;
        max-width: calc(100% - 40px);
    }
}

/* Enhanced Flow Management Styles */

/* File Operations Section */
.file-operations {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.file-operations h4 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 14px;
    font-weight: 600;
}

.file-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 10px;
}

.file-controls .btn {
    flex: 1;
    min-width: 120px;
    font-size: 12px;
    padding: 8px 12px;
}

.help-text {
    font-size: 11px;
    color: #666;
    margin-top: 5px;
    line-height: 1.3;
}

/* Validation Section */
.validation-section {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.validation-section h4 {
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #333;
}

.validation-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 10px;
}

.validation-controls .btn {
    flex: 1;
    min-width: 110px;
    font-size: 12px;
    padding: 8px 12px;
}

.validation-status {
    padding: 8px 12px;
    border-radius: 6px;
    background: #e9ecef;
    border: 1px solid #ced4da;
}

.validation-status.valid {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.validation-status.invalid {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.validation-status.warning {
    background: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

/* Enhanced Flow Events */
.enhanced-flow-list {
    margin-bottom: 20px;
}

.enhanced-flow-list h4 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 14px;
    font-weight: 600;
}

.flow-event {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 12px;
    overflow: hidden;
    transition: all 0.2s ease;
}

.flow-event:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.event-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 15px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.event-type {
    background: #007bff;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.event-name {
    font-weight: 500;
    color: #495057;
    margin-left: 10px;
    flex: 1;
}

.event-status {
    display: flex;
    align-items: center;
}

.status-indicator {
    font-size: 12px;
    margin-right: 5px;
}

.status-indicator[data-status="pending"] {
    color: #6c757d;
}

.status-indicator[data-status="success"] {
    color: #28a745;
}

.status-indicator[data-status="error"] {
    color: #dc3545;
}

.status-indicator[data-status="warning"] {
    color: #ffc107;
}

.event-actions {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
    align-items: center;
    margin-top: 8px;
}

.event-actions .btn {
    flex: 0 0 auto;
    min-width: 60px;
    padding: 4px 8px;
    font-size: 11px;
    line-height: 1.2;
    white-space: nowrap;
}

.event-description {
    padding: 10px 15px;
    font-size: 12px;
    color: #6c757d;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

/* Flow Global Actions */
.flow-global-actions {
    margin-top: 20px;
    padding: 15px;
    background: #e8f4f8;
    border-radius: 8px;
    border: 1px solid #b3dadd;
}

.flow-global-actions .btn {
    margin-right: 8px;
    margin-bottom: 8px;
    font-size: 12px;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.modal-header h3 {
    margin: 0;
    color: #495057;
}

.modal-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #6c757d;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    background: #e9ecef;
    color: #333;
}

.modal-body {
    padding: 20px;
    overflow-y: auto;
    flex: 1;
}

.modal-buttons {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(2px);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e9ecef;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 14px;
    color: #6c757d;
    text-align: center;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10001;
    pointer-events: none;
}

.toast {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    padding: 12px 16px;
    margin-bottom: 10px;
    max-width: 300px;
    pointer-events: auto;
    animation: toastSlideIn 0.3s ease;
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.toast.success {
    border-left: 4px solid #28a745;
}

.toast.error {
    border-left: 4px solid #dc3545;
}

.toast.warning {
    border-left: 4px solid #ffc107;
}

.toast.info {
    border-left: 4px solid #17a2b8;
}

@keyframes toastSlideIn {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.toast-icon {
    font-size: 16px;
    margin-top: 1px;
}

.toast-content {
    flex: 1;
    font-size: 13px;
    line-height: 1.4;
}

.toast-close {
    background: none;
    border: none;
    cursor: pointer;
    color: #6c757d;
    padding: 0;
    font-size: 16px;
    line-height: 1;
    margin-left: 10px;
}

.toast-close:hover {
    color: #333;
}

/* Responsive Design */
@media (max-width: 480px) {
    .event-actions {
        justify-content: space-between;
    }
    
    .event-actions .btn {
        flex: 1;
        min-width: 0;
        justify-content: center;
    }
    
    .flow-controls-enhanced {
        flex-direction: column;
    }
    
    .flow-controls-enhanced .btn {
        justify-content: center;
    }
    
    #venus-element-manager {
        left: 5px !important;
        right: 5px !important;
        width: auto !important;
    }
}

/* Modal and Button Improvements */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(3px);
    padding: 20px;
}

.modal-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.3);
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease;
    width: auto;
    min-width: 400px;
}

.modal-footer {
    padding: 15px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Enhanced Button Spacing */
.event-actions {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
    align-items: center;
    margin-top: 8px;
}

.event-actions .btn {
    flex: 0 0 auto;
    min-width: 60px;
    padding: 4px 8px;
    font-size: 11px;
    line-height: 1.2;
    white-space: nowrap;
}

/* Fix button overflow in file controls */
.file-controls .btn,
.validation-controls .btn {
    word-break: break-word;
    text-overflow: ellipsis;
    overflow: hidden;
}

/* Better responsive behavior */
@media (max-width: 520px) {
    body {
        width: 480px;
    }
    
    .file-controls,
    .validation-controls {
        flex-direction: column;
    }
    
    .file-controls .btn,
    .validation-controls .btn {
        width: 100%;
        min-width: unset;
    }
    
    .event-actions {
        flex-direction: column;
        gap: 4px;
    }
    
    .event-actions .btn {
        width: 100%;
        min-width: unset;
    }

    .flow-type-buttons {
        flex-direction: column;
    }

    .flow-type-buttons .btn {
        min-width: auto;
        width: 100%;
    }
}

/* Flow Type Management */
.flow-type-management {
    background: linear-gradient(135deg, #f3e5f5, #e8eaf6);
    border: 1px solid #d1c4e9;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
}

.flow-type-management h4 {
    font-size: 14px;
    font-weight: 600;
    color: #4a148c;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.flow-type-indicator-container {
    margin-bottom: 12px;
    text-align: center;
}

.flow-type-indicator {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.flow-type-indicator.pre-login {
    background: linear-gradient(135deg, #4caf50, #66bb6a);
    color: white;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.flow-type-indicator.post-login {
    background: linear-gradient(135deg, #2196f3, #42a5f5);
    color: white;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
}

.flow-type-controls {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.flow-type-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.flow-type-buttons .btn {
    flex: 1;
    min-width: 140px;
    padding: 10px 16px;
    font-size: 12px;
    font-weight: 600;
    border-radius: 6px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.flow-type-buttons .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.flow-type-buttons .btn:hover::before {
    left: 100%;
}

.flow-type-buttons #flowTypePreLogin {
    background: linear-gradient(135deg, #4caf50, #45a049);
    border: none;
    color: white;
    box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
}

.flow-type-buttons #flowTypePreLogin:hover {
    background: linear-gradient(135deg, #45a049, #3d8b40);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
    transform: translateY(-1px);
}

.flow-type-buttons #flowTypePostLogin {
    background: linear-gradient(135deg, #2196f3, #1976d2);
    border: none;
    color: white;
    box-shadow: 0 3px 10px rgba(33, 150, 243, 0.3);
}

.flow-type-buttons #flowTypePostLogin:hover {
    background: linear-gradient(135deg, #1976d2, #1565c0);
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4);
    transform: translateY(-1px);
}

.flow-type-description {
    background: rgba(255, 255, 255, 0.7);
    border-radius: 6px;
    padding: 10px;
    border-left: 4px solid #9c27b0;
}

.flow-type-description .help-text {
    font-size: 11px;
    line-height: 1.4;
    color: #4a148c;
    margin: 0;
}

.flow-type-description strong {
    color: #6a1b9a;
    font-weight: 600;
}

/* Enhanced Flow Type Animations */
@keyframes flowTypeGlow {
    0%, 100% {
        box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
    }
    50% {
        box-shadow: 0 4px 16px rgba(76, 175, 80, 0.5);
    }
}

.flow-type-indicator.pre-login.active {
    animation: flowTypeGlow 2s infinite;
}

@keyframes flowTypeGlowBlue {
    0%, 100% {
        box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
    }
    50% {
        box-shadow: 0 4px 16px rgba(33, 150, 243, 0.5);
    }
}

.flow-type-indicator.post-login.active {
    animation: flowTypeGlowBlue 2s infinite;
}
