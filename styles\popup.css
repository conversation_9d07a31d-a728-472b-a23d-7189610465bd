/* Chrome Extension Automation Bot - Popup Styles */

:root {
    --primary-color: #4caf50;
    --secondary-color: #2196f3;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --success-color: #4caf50;
    --background-color: #ffffff;
    --border-color: #e0e0e0;
    --text-color: #333333;
    --accent-color: #6c757d;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 14px;
    color: var(--text-color);
    background-color: var(--background-color);
    width: 400px;
    max-height: 600px;
    overflow-y: auto;
}

.container {
    padding: 0;
}

/* Header */
.header {
    background: linear-gradient(135deg, #4caf50, #2196f3);
    color: white;
    padding: 16px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    position: relative;
}

.logo {
    width: 32px;
    height: 32px;
    border-radius: 6px;
}

.header h1 {
    font-size: 18px;
    font-weight: 600;
    flex: 1;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    background: rgba(255, 255, 255, 0.15);
    padding: 4px 8px;
    border-radius: 12px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #4caf50;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Quick Run Section */
.quick-run-section {
    background: linear-gradient(135deg, #e8f5e8, #e3f2fd);
    border-bottom: 2px solid #4caf50;
    padding: 20px;
    margin: 0;
}

.quick-run-section h3 {
    font-size: 16px;
    font-weight: 600;
    color: #2e7d32;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.quick-run-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 15px;
}

.info-item {
    font-size: 12px;
    color: #555;
}

.info-item strong {
    color: #2e7d32;
    font-weight: 600;
}

.btn-primary-action {
    background: linear-gradient(135deg, #4caf50, #45a049) !important;
    border: none !important;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3) !important;
    font-weight: 600 !important;
    font-size: 16px !important;
    padding: 12px 24px !important;
    width: 100% !important;
    transition: all 0.3s ease !important;
}

.btn-primary-action:hover {
    background: linear-gradient(135deg, #45a049, #3d8b40) !important;
    box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4) !important;
    transform: translateY(-1px) !important;
}

/* Navigation Tabs */
.nav-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid var(--border-color);
    padding: 0;
}

.tab-button {
    flex: 1;
    padding: 12px 8px;
    border: none;
    background: transparent;
    font-size: 12px;
    font-weight: 500;
    color: var(--accent-color);
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 3px solid transparent;
}

.tab-button:hover {
    background: rgba(33, 150, 243, 0.1);
    color: var(--secondary-color);
}

.tab-button.active {
    color: var(--secondary-color);
    border-bottom-color: var(--secondary-color);
    background: white;
    font-weight: 600;
}

/* Tab Content */
.tab-content {
    display: none;
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.tab-content.active {
    display: block;
}

/* Sections */
.section {
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.section h3 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Form Elements */
.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    font-size: 12px;
    font-weight: 500;
    margin-bottom: 6px;
    color: var(--text-color);
}

.form-group input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-group input:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.form-group small {
    display: block;
    font-size: 11px;
    color: var(--accent-color);
    margin-top: 4px;
}

/* Buttons */
.btn {
    padding: 8px 16px;
    border: 1px solid transparent;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-right: 8px;
    margin-bottom: 8px;
    display: inline-block;
    text-decoration: none;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: #45a049;
    border-color: #45a049;
}

.btn-secondary {
    background: #6c757d;
    color: white;
    border-color: #6c757d;
}

.btn-secondary:hover {
    background: #5a6268;
    border-color: #5a6268;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
    border-color: var(--warning-color);
}

.btn-warning:hover {
    background: #e68900;
    border-color: #e68900;
}

.btn-success {
    background: var(--success-color);
    color: white;
    border-color: var(--success-color);
}

.btn-success:hover {
    background: #45a049;
    border-color: #45a049;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
    border-color: var(--danger-color);
}

.btn-danger:hover {
    background: #d32f2f;
    border-color: #d32f2f;
}

.btn-large {
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
}

.btn-small {
    padding: 4px 8px;
    font-size: 11px;
}

/* Data Controls */
.data-controls, .flow-controls, .execution-controls {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
    flex-wrap: wrap;
}

/* Data Status */
.data-status {
    padding: 12px;
    background: #f8f9fa;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    margin-bottom: 16px;
    font-size: 12px;
}

/* Data Preview */
.data-preview {
    max-height: 300px;
    overflow: auto;
    border: 1px solid var(--border-color);
    border-radius: 6px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 11px;
}

.data-table th {
    background: #f8f9fa;
    padding: 8px 6px;
    text-align: left;
    font-weight: 600;
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
}

.data-table td {
    padding: 6px;
    border-bottom: 1px solid #f0f0f0;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.data-table tr:hover {
    background: #f8f9fa;
}

/* Flow Events */
.flow-list {
    max-height: 250px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: #fafafa;
}

.flow-event {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
    background: white;
    margin-bottom: 1px;
}

.flow-event:last-child {
    border-bottom: none;
}

.event-info {
    flex: 1;
}

.event-type {
    font-size: 11px;
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: 4px;
}

.event-details {
    font-size: 12px;
    color: var(--text-color);
    line-height: 1.3;
}

.event-actions {
    display: flex;
    gap: 4px;
}

/* Execution Status */
.execution-status {
    margin-bottom: 16px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--success-color), #66bb6a);
    width: 0%;
    transition: width 0.3s ease;
}

.execution-status .status-text {
    font-size: 12px;
    color: var(--accent-color);
}

/* Execution Log */
.execution-log {
    max-height: 200px;
    overflow-y: auto;
    background: #f8f9fa;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 12px;
    font-family: 'Consolas', monospace;
    font-size: 11px;
    line-height: 1.4;
}

.log-entry {
    margin-bottom: 4px;
    padding: 2px 0;
}

.log-timestamp {
    color: var(--accent-color);
    font-weight: 500;
}

.log-info {
    color: var(--secondary-color);
}

.log-success {
    color: var(--success-color);
}

.log-warning {
    color: var(--warning-color);
}

.log-error {
    color: var(--danger-color);
}

/* Debug Info */
.debug-info {
    background: #f8f9fa;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 16px;
    margin-top: 16px;
}

/* Flow Quick Actions */
.flow-quick-actions {
    text-align: center;
}

.flow-quick-actions h4 {
    color: var(--text-color);
    margin-bottom: 12px;
}

/* Responsive Design */
@media (max-width: 480px) {
    body {
        width: 100%;
        max-width: 400px;
    }
    
    .quick-run-info {
        grid-template-columns: 1fr;
    }
    
    .data-controls, .flow-controls, .execution-controls {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
        margin-right: 0;
    }
}

/* Animation for success states */
.success-pulse {
    animation: successPulse 1s ease-in-out;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Text Search Styles */
.text-search-info {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 15px;
    border-left: 4px solid #007bff;
}

.search-options {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    align-items: center;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
}

.search-controls {
    display: flex;
    gap: 8px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.search-navigation {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    margin-bottom: 15px;
}

.search-results-info {
    font-weight: 500;
    margin-bottom: 10px;
    color: #495057;
    text-align: center;
}

.navigation-controls {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 4px;
}

.keyboard-shortcuts {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    margin-top: 15px;
}

.keyboard-shortcuts h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #495057;
}

.keyboard-shortcuts ul {
    margin: 0;
    padding-left: 20px;
    font-size: 12px;
    color: #6c757d;
}

.keyboard-shortcuts li {
    margin-bottom: 4px;
}

.keyboard-shortcuts strong {
    color: #495057;
    font-family: 'Courier New', monospace;
    background: #e9ecef;
    padding: 2px 4px;
    border-radius: 3px;
}

/* Post-Login Automation Button */
#runPostLoginAutomation {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
    color: white;
    margin-top: 8px;
    transition: all 0.3s ease;
}

#runPostLoginAutomation:hover {
    background: linear-gradient(135deg, #218838, #1ea085);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

#runPostLoginAutomation:active {
    transform: translateY(0);
}

/* Enhanced button styles for text search */
.search-controls .btn {
    min-width: 80px;
}

.search-controls .btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.search-controls .btn-secondary {
    background: linear-gradient(135deg, #6c757d, #545b62);
}

/* Enhanced Flow Management Styles */
.file-operations-section {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 4px solid #007bff;
}

.file-operations-section h4 {
    margin: 0 0 12px 0;
    color: #495057;
    font-size: 14px;
    font-weight: 600;
}

.file-controls {
    display: flex;
    gap: 8px;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.file-info {
    font-size: 12px;
    color: #6c757d;
    font-style: italic;
}

.validation-section {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 4px solid #28a745;
}

.validation-section h4 {
    margin: 0 0 12px 0;
    color: #495057;
    font-size: 14px;
    font-weight: 600;
}

.validation-controls {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
    flex-wrap: wrap;
}

.validation-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
}

.status-indicator {
    font-size: 12px;
    font-weight: bold;
}

.status-indicator[data-status="pending"] {
    color: #6c757d;
}

.status-indicator[data-status="success"] {
    color: #28a745;
}

.status-indicator[data-status="error"] {
    color: #dc3545;
}

.status-indicator[data-status="testing"] {
    color: #ffc107;
}

.flow-list-container {
    margin-bottom: 20px;
}

.flow-list-container h4 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 16px;
    font-weight: 600;
}

.flow-event {
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 12px;
    padding: 15px;
    transition: all 0.2s ease;
}

.flow-event:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.event-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.event-info {
    flex: 1;
}

.event-type {
    font-weight: 600;
    color: #495057;
    font-size: 13px;
    margin-bottom: 4px;
}

.event-details {
    font-size: 12px;
    color: #6c757d;
    line-height: 1.4;
}

.event-actions {
    display: flex;
    gap: 6px;
    flex-shrink: 0;
}

.event-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    padding-top: 8px;
    border-top: 1px solid #f1f3f4;
}

.status-text {
    color: #6c757d;
}

.flow-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 4px solid #17a2b8;
}

.flow-info h4 {
    margin: 0 0 12px 0;
    color: #495057;
    font-size: 14px;
    font-weight: 600;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
}

.info-item {
    font-size: 12px;
}

.info-item strong {
    color: #495057;
    display: block;
    margin-bottom: 2px;
}

.no-flow-message {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px dashed #dee2e6;
}

/* Modal Styles for Results Display */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    padding: 20px;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #dee2e6;
}

.modal-header h3 {
    margin: 0;
    color: #495057;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #495057;
}

.modal-footer {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #dee2e6;
    text-align: right;
}

/* Validation Results Styles */
.validation-results .validation-status.valid {
    color: #28a745;
    font-weight: 600;
    margin-bottom: 15px;
}

.validation-results .validation-status.invalid {
    color: #dc3545;
    font-weight: 600;
    margin-bottom: 15px;
}

.validation-errors, .validation-warnings {
    margin-bottom: 15px;
}

.validation-errors h5 {
    color: #dc3545;
    margin: 0 0 8px 0;
    font-size: 14px;
}

.validation-warnings h5 {
    color: #ffc107;
    margin: 0 0 8px 0;
    font-size: 14px;
}

.validation-errors ul, .validation-warnings ul {
    margin: 0;
    padding-left: 20px;
    font-size: 13px;
}

/* Dry Run Results Styles */
.dry-run-results .summary {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 15px;
}

.dry-run-results .summary p {
    margin: 0 0 5px 0;
    font-size: 13px;
}

.event-result {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 10px;
    margin-bottom: 8px;
}

.event-result.simulated {
    border-left: 4px solid #28a745;
}

.event-result.skipped {
    border-left: 4px solid #ffc107;
}

.event-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.event-name {
    font-weight: 600;
    font-size: 13px;
}

.event-type {
    font-size: 11px;
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    color: #495057;
}

.event-status {
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 3px;
    text-transform: uppercase;
    font-weight: 600;
}

.event-result.simulated .event-status {
    background: #d4edda;
    color: #155724;
}

.event-result.skipped .event-status {
    background: #fff3cd;
    color: #856404;
}

.event-notes {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
}

/* Responsive Design for Enhanced Features */
@media (max-width: 400px) {
    .file-controls, .validation-controls {
        flex-direction: column;
    }

    .file-controls .btn, .validation-controls .btn {
        width: 100%;
        margin-bottom: 5px;
    }

    .event-actions {
        flex-direction: column;
        gap: 4px;
    }

    .event-actions .btn {
        font-size: 11px;
        padding: 4px 8px;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        margin: 20px;
        max-width: calc(100% - 40px);
    }
}

/* Enhanced Flow Management Styles */

/* File Operations Section */
.file-operations {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.file-operations h4 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 14px;
    font-weight: 600;
}

.file-controls {
    display: flex;
    gap: 8px;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.file-controls .btn {
    flex: 1;
    min-width: 100px;
    font-size: 12px;
    padding: 8px 12px;
}

.help-text {
    display: block;
    font-size: 11px;
    color: #6c757d;
    font-style: italic;
    margin-top: 5px;
}

/* Validation Section */
.validation-section {
    margin-bottom: 20px;
    padding: 15px;
    background: #f1f3f5;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.validation-section h4 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 14px;
    font-weight: 600;
}

.validation-controls {
    display: flex;
    gap: 8px;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.validation-controls .btn {
    flex: 1;
    font-size: 12px;
    padding: 8px 12px;
}

.validation-status {
    padding: 8px 12px;
    border-radius: 6px;
    background: #e9ecef;
    border: 1px solid #ced4da;
}

.validation-status.valid {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.validation-status.invalid {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.validation-status.warning {
    background: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

/* Enhanced Flow Events */
.enhanced-flow-list {
    margin-bottom: 20px;
}

.enhanced-flow-list h4 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 14px;
    font-weight: 600;
}

.flow-event {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 12px;
    overflow: hidden;
    transition: all 0.2s ease;
}

.flow-event:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.event-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 15px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.event-type {
    background: #007bff;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.event-name {
    font-weight: 500;
    color: #495057;
    margin-left: 10px;
    flex: 1;
}

.event-status {
    display: flex;
    align-items: center;
}

.status-indicator {
    font-size: 12px;
    margin-right: 5px;
}

.status-indicator[data-status="pending"] {
    color: #6c757d;
}

.status-indicator[data-status="success"] {
    color: #28a745;
}

.status-indicator[data-status="error"] {
    color: #dc3545;
}

.status-indicator[data-status="warning"] {
    color: #ffc107;
}

.event-actions {
    display: flex;
    gap: 8px;
    padding: 10px 15px;
    background: #ffffff;
}

.event-actions .btn {
    font-size: 11px;
    padding: 6px 10px;
}

.event-description {
    padding: 10px 15px;
    font-size: 12px;
    color: #6c757d;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

/* Flow Global Actions */
.flow-global-actions {
    margin-top: 20px;
    padding: 15px;
    background: #e8f4f8;
    border-radius: 8px;
    border: 1px solid #b3dadd;
}

.flow-global-actions .btn {
    margin-right: 8px;
    margin-bottom: 8px;
    font-size: 12px;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.modal-header h3 {
    margin: 0;
    color: #495057;
}

.modal-close {
    background: none;
    border: none;
    font-size: 20px;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    background: #e9ecef;
    color: #495057;
}

.modal-body {
    padding: 20px;
    overflow-y: auto;
    flex: 1;
}

.modal-footer {
    padding: 15px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Validation Results */
.validation-results {
    font-size: 14px;
}

.validation-results h4 {
    margin: 0 0 15px 0;
    color: #495057;
}

.validation-status.valid {
    color: #155724;
    background: #d4edda;
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 15px;
}

.validation-status.invalid {
    color: #721c24;
    background: #f8d7da;
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 15px;
}

.validation-errors,
.validation-warnings {
    margin-bottom: 15px;
}

.validation-errors h5,
.validation-warnings h5 {
    margin: 0 0 8px 0;
    font-size: 13px;
}

.validation-errors h5 {
    color: #721c24;
}

.validation-warnings h5 {
    color: #856404;
}

.validation-errors ul,
.validation-warnings ul {
    margin: 0;
    padding-left: 20px;
    font-size: 12px;
}

.validation-errors li {
    color: #721c24;
    margin-bottom: 4px;
}

.validation-warnings li {
    color: #856404;
    margin-bottom: 4px;
}

.preflight-results {
    background: #e7f3ff;
    padding: 10px;
    border-radius: 6px;
    font-size: 13px;
}

.preflight-results h5 {
    margin: 0 0 8px 0;
    color: #004085;
}

.preflight-results p {
    margin: 4px 0;
    color: #004085;
}

/* Dry Run Results */
.dry-run-results {
    font-size: 14px;
}

.dry-run-results h4 {
    margin: 0 0 15px 0;
    color: #495057;
}

.dry-run-results .summary {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 15px;
}

.dry-run-results .summary p {
    margin: 4px 0;
    font-size: 13px;
}

.event-results h5 {
    margin: 0 0 10px 0;
    font-size: 13px;
    color: #495057;
}

.event-result {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    border-radius: 6px;
    margin-bottom: 6px;
    font-size: 12px;
}

.event-result.ready {
    background: #d4edda;
    color: #155724;
}

.event-result.warning {
    background: #fff3cd;
    color: #856404;
}

.event-result.skipped {
    background: #e2e3e5;
    color: #6c757d;
}

.event-result.simulated {
    background: #cce7ff;
    color: #004085;
}

.event-name {
    font-weight: 500;
    flex: 1;
}

.event-status {
    font-size: 11px;
    text-transform: uppercase;
    font-weight: 600;
}

.event-notes {
    font-size: 11px;
    margin-top: 4px;
    opacity: 0.8;
}

/* Test Results */
.all-events-test-results {
    font-size: 14px;
}

.all-events-test-results h4 {
    margin: 0 0 15px 0;
    color: #495057;
}

.all-events-test-results .summary {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 15px;
}

.all-events-test-results .summary p {
    margin: 4px 0;
    font-size: 13px;
}

.detailed-results {
    max-height: 300px;
    overflow-y: auto;
}

.test-result {
    border: 1px solid #e9ecef;
    border-radius: 6px;
    margin-bottom: 8px;
    overflow: hidden;
}

.test-result.success {
    border-color: #c3e6cb;
}

.test-result.error {
    border-color: #f5c6cb;
}

.result-header {
    display: flex;
    align-items: center;
    padding: 10px 12px;
    background: #f8f9fa;
    gap: 10px;
}

.result-header .status-icon {
    font-size: 14px;
}

.result-header .event-name {
    font-weight: 500;
    flex: 1;
    font-size: 13px;
}

.result-header .event-type {
    background: #6c757d;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    text-transform: uppercase;
}

.error-details,
.test-notes {
    padding: 8px 12px;
    border-top: 1px solid #e9ecef;
    font-size: 12px;
}

.error-details {
    background: #f8d7da;
    color: #721c24;
}

.test-notes {
    background: #ffffff;
    color: #6c757d;
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 16px;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 10001;
    max-width: 300px;
    font-size: 13px;
    animation: slideInRight 0.3s ease;
}

.notification-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.notification-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.notification-warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.notification-info {
    background: #cce7ff;
    color: #004085;
    border: 1px solid #b3d7ff;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Legacy Controls Section */
.legacy-controls {
    margin-bottom: 20px;
    padding: 15px;
    background: #fff8e1;
    border-radius: 8px;
    border: 1px solid #ffecb3;
}

.legacy-controls h4 {
    margin: 0 0 10px 0;
    color: #f57c00;
    font-size: 14px;
    font-weight: 600;
}

.legacy-controls .flow-controls {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.legacy-controls .btn {
    font-size: 12px;
    padding: 8px 12px;
}

/* Button enhancements */
.btn-info {
    background: #17a2b8;
    border-color: #17a2b8;
    color: white;
}

.btn-info:hover {
    background: #138496;
    border-color: #117a8b;
}

/* Responsive adjustments */
@media (max-width: 400px) {
    .file-controls {
        flex-direction: column;
    }
    
    .validation-controls {
        flex-direction: column;
    }
    
    .file-controls .btn,
    .validation-controls .btn {
        flex: none;
        width: 100%;
    }
    
    .modal-content {
        width: 95%;
        margin: 10px;
    }
}

/* Enhanced Event Cards */
.flow-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    border: 1px solid #e9ecef;
}

.flow-info h5 {
    margin: 0 0 8px 0;
    color: #495057;
    font-size: 16px;
    font-weight: 600;
}

.flow-description {
    margin: 0 0 10px 0;
    color: #6c757d;
    font-size: 13px;
    line-height: 1.4;
}

.flow-stats {
    display: flex;
    gap: 15px;
}

.flow-stats .stat {
    font-size: 12px;
    color: #495057;
    font-weight: 500;
}

.no-flow-message {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px dashed #ced4da;
}

.flow-events {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;
}

.event-card {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.2s ease;
}

.event-card:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.event-card .event-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 12px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.event-type-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    color: white;
}

.event-type-click {
    background: #007bff;
}

.event-type-input {
    background: #28a745;
}

.event-type-navigate {
    background: #17a2b8;
}

.event-type-wait {
    background: #ffc107;
    color: #212529;
}

.event-type-search {
    background: #6f42c1;
}

.event-type-conditional {
    background: #fd7e14;
}

.event-type-extract {
    background: #20c997;
}

.event-type-default {
    background: #6c757d;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 4px;
}

.status-dot[data-status="pending"] {
    background: #6c757d;
}

.status-dot[data-status="success"] {
    background: #28a745;
}

.status-dot[data-status="error"] {
    background: #dc3545;
}

.status-dot[data-status="warning"] {
    background: #ffc107;
}

.event-content {
    padding: 12px;
}

.event-content h6 {
    margin: 0 0 6px 0;
    font-size: 14px;
    font-weight: 600;
    color: #495057;
}

.event-content .event-description {
    margin: 0 0 10px 0;
    font-size: 12px;
    color: #6c757d;
    line-height: 1.3;
}

.event-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.detail-item {
    font-size: 11px;
    color: #495057;
    padding: 2px 0;
}

.detail-item strong {
    color: #343a40;
    min-width: 60px;
    display: inline-block;
}

.event-actions {
    display: flex;
    gap: 6px;
    padding: 10px 12px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.event-actions .btn {
    flex: 1;
    font-size: 10px;
    padding: 6px 8px;
    min-height: 28px;
}

.btn-outline {
    background: transparent;
    border: 1px solid #ced4da;
    color: #495057;
}

.btn-outline:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.flow-controls-enhanced {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    padding: 15px;
    background: #e8f4f8;
    border-radius: 8px;
    border: 1px solid #b3dadd;
}

.flow-controls-enhanced .btn {
    flex: 1;
    font-size: 12px;
    padding: 10px;
}

/* Event Edit Form */
.event-edit-form {
    max-width: 100%;
}

.event-edit-form .form-group {
    margin-bottom: 15px;
}

.event-edit-form label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    font-size: 13px;
    color: #495057;
}

.event-edit-form input,
.event-edit-form textarea,
.event-edit-form select {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 13px;
    box-sizing: border-box;
}

.event-edit-form textarea {
    resize: vertical;
    min-height: 60px;
}

.form-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.form-actions .btn {
    flex: 1;
    font-size: 12px;
    padding: 8px 12px;
}

/* Event Details View */
.event-details-view {
    max-width: 100%;
}

.detail-section {
    margin-bottom: 20px;
}

.detail-section h5 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #495057;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 5px;
}

.details-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 13px;
}

.details-table td {
    padding: 6px 8px;
    border-bottom: 1px solid #f1f3f5;
    vertical-align: top;
}

.details-table td:first-child {
    width: 30%;
    color: #6c757d;
}

.json-display {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 10px;
    font-family: 'Courier New', monospace;
    font-size: 11px;
    overflow-x: auto;
    white-space: pre-wrap;
    word-break: break-all;
    color: #495057;
    max-height: 200px;
    overflow-y: auto;
}

/* Responsive Enhancements */
@media (max-width: 400px) {
    .flow-stats {
        flex-direction: column;
        gap: 5px;
    }
    
    .event-actions {
        flex-direction: column;
        gap: 8px;
    }
    
    .event-actions .btn {
        flex: none;
        width: 100%;
    }
    
    .flow-controls-enhanced {
        flex-direction: column;
    }
    
    .flow-controls-enhanced .btn {
        flex: none;
        width: 100%;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .form-actions .btn {
        flex: none;
        width: 100%;
    }
}

/* Enhanced Event Testing Styles */
.event-status {
    display: flex;
    align-items: center;
    gap: 5px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    transition: all 0.3s ease;
}

.status-dot.status-pending {
    background-color: #6c757d;
}

.status-dot.status-testing {
    background-color: #007bff;
    animation: pulse 1s infinite;
}

.status-dot.status-executing {
    background-color: #ffc107;
    animation: pulse 1s infinite;
}

.status-dot.status-success {
    background-color: #28a745;
}

.status-dot.status-error {
    background-color: #dc3545;
}

.status-dot.status-warning {
    background-color: #fd7e14;
}

@keyframes pulse {
    0% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.5; transform: scale(1.2); }
    100% { opacity: 1; transform: scale(1); }
}

/* Event Test Results Modal */
.event-test-results {
    padding: 20px;
}

.test-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 5px;
    background: #f8f9fa;
}

.test-status {
    font-weight: bold;
}

.test-status.success {
    color: #28a745;
}

.test-status.error {
    color: #dc3545;
}

.test-duration {
    font-size: 12px;
    color: #6c757d;
}

.test-error {
    margin-bottom: 15px;
    padding: 10px;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 5px;
    color: #721c24;
}

.test-details {
    margin-bottom: 15px;
}

.test-details pre {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    font-size: 11px;
    max-height: 200px;
    overflow-y: auto;
}

/* Enhanced Event Card Buttons */
.event-actions {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
    margin-top: 10px;
}

.event-actions .btn {
    padding: 4px 8px;
    font-size: 11px;
    border-radius: 3px;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-test {
    background: #17a2b8;
    color: white;
}

.btn-test:hover {
    background: #138496;
}

.btn-execute {
    background: #28a745;
    color: white;
}

.btn-execute:hover {
    background: #218838;
}

.btn-edit {
    background: #ffc107;
    color: #212529;
}

.btn-edit:hover {
    background: #e0a800;
}

.btn-delete {
    background: #dc3545;
    color: white;
}

.btn-delete:hover {
    background: #c82333;
}

.btn-highlight {
    background: #6f42c1;
    color: white;
}

.btn-highlight:hover {
    background: #5a32a3;
}

.btn-manager {
    background: #fd7e14;
    color: white;
}

.btn-manager:hover {
    background: #e8690b;
}

/* Element Manager Interface (appears on webpage) */
#venus-element-manager {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    box-shadow: 0 8px 32px rgba(0,0,0,0.3) !important;
    backdrop-filter: blur(10px) !important;
}

#venus-element-manager * {
    box-sizing: border-box !important;
}

#venus-element-manager input,
#venus-element-manager select,
#venus-element-manager textarea,
#venus-element-manager button {
    font-family: inherit !important;
}

#venus-element-manager .form-group {
    margin-bottom: 12px;
}

#venus-element-manager label {
    display: block;
    margin-bottom: 4px;
    font-weight: 600;
    color: #333;
}

#venus-element-manager input[type="text"],
#venus-element-manager select,
#venus-element-manager textarea {
    width: 100%;
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 13px;
    transition: border-color 0.2s ease;
}

#venus-element-manager input[type="text"]:focus,
#venus-element-manager select:focus,
#venus-element-manager textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

#venus-element-manager button {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

#venus-element-manager button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

#venus-element-manager button:active {
    transform: translateY(0);
}

#venus-element-manager #venus-results {
    font-size: 12px;
    line-height: 1.4;
}

#venus-element-manager .status-icon {
    margin-right: 5px;
}

/* Enhanced Flow Controls */
.flow-controls-enhanced {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.flow-controls-enhanced .btn {
    padding: 8px 12px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 5px;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.flow-controls-enhanced .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

/* Enhanced Event Type Badges */
.event-type-badge {
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-flex;
    align-items: center;
    gap: 3px;
}

.event-type-click {
    background: #e3f2fd;
    color: #1976d2;
}

.event-type-input {
    background: #e8f5e8;
    color: #2e7d32;
}

.event-type-navigate {
    background: #e0f2f1;
    color: #00695c;
}

.event-type-wait {
    background: #fff3e0;
    color: #ef6c00;
}

.event-type-text_search_click {
    background: #f3e5f5;
    color: #7b1fa2;
}

.event-type-conditional_action {
    background: #fff8e1;
    color: #f57c00;
}

.event-type-extract {
    background: #e0f7fa;
    color: #00838f;
}

/* Modal Enhancements */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(3px);
}

.modal-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.3);
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.modal-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.modal-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #6c757d;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    background: #e9ecef;
    color: #333;
}

.modal-body {
    padding: 20px;
}

.modal-buttons {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(2px);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e9ecef;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 14px;
    color: #6c757d;
    text-align: center;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10001;
    pointer-events: none;
}

.toast {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    padding: 12px 16px;
    margin-bottom: 10px;
    max-width: 300px;
    pointer-events: auto;
    animation: toastSlideIn 0.3s ease;
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.toast.success {
    border-left: 4px solid #28a745;
}

.toast.error {
    border-left: 4px solid #dc3545;
}

.toast.warning {
    border-left: 4px solid #ffc107;
}

.toast.info {
    border-left: 4px solid #17a2b8;
}

@keyframes toastSlideIn {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.toast-icon {
    font-size: 16px;
    margin-top: 1px;
}

.toast-content {
    flex: 1;
    font-size: 13px;
    line-height: 1.4;
}

.toast-close {
    background: none;
    border: none;
    cursor: pointer;
    color: #6c757d;
    padding: 0;
    font-size: 16px;
    line-height: 1;
    margin-left: 10px;
}

.toast-close:hover {
    color: #333;
}

/* Responsive Design */
@media (max-width: 480px) {
    .event-actions {
        justify-content: space-between;
    }
    
    .event-actions .btn {
        flex: 1;
        min-width: 0;
        justify-content: center;
    }
    
    .flow-controls-enhanced {
        flex-direction: column;
    }
    
    .flow-controls-enhanced .btn {
        justify-content: center;
    }
    
    #venus-element-manager {
        left: 5px !important;
        right: 5px !important;
        width: auto !important;
    }
}
