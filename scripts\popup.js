// Venus-Millware AutoFill - Popup Script
// Alat otomatisasi untuk pengisian form dan input data otomatis
// Developer: Atha Rizki Pangestu - IT Rebinmas (Delloyd Group)

class AutomationBotPopup {
    constructor() {
        this.currentTab = 'config';
        this.automationData = null;
        this.flowEvents = [];
        this.isExecuting = false;
        this.apiService = new ApiService();
        this.flowManager = null; // Will be initialized after DOM load
        this.currentFlowData = null;
        
        // Default configuration for timesheet automation
        this.defaultConfig = {
            apiBaseUrl: 'http://localhost:5173/api',
            targetUrl: 'http://millwarep3.rebinmas.com:8003/',
            username: 'adm075',
            password: 'adm075',
            apiKey: '',
            refreshInterval: 30000,
            retryAttempts: 3,
            delayInterval: 1000 // 1 second between steps
        };

        // Flow state management
        this.currentFlowType = 'pre-login'; // 'pre-login' or 'post-login'

        // Predefined Pre-Login flow (complete authentication process)
        this.preLoginFlow = [
            {
                type: "navigate",
                url: "http://millwarep3.rebinmas.com:8003/",
                description: "Navigasi ke halaman login sistem",
                ensurePageReady: true,
                timeout: 15000
            },
            {
                type: "wait",
                duration: 500,
                description: "Menunggu halaman login selesai dimuat"
            },
            {
                type: "input",
                selector: "#txtUsername",
                selectorType: "css",
                value: "adm075",
                description: "Mengisi field username dengan kredensial yang valid",
                clearFirst: true,
                simulateTyping: true,
                typingDelay: 50
            },
            {
                type: "wait",
                duration: 300,
                description: "Menunggu setelah input username"
            },
            {
                type: "input",
                selector: "#txtPassword",
                selectorType: "css",
                value: "adm075",
                description: "Mengisi field password dengan kredensial yang valid",
                clearFirst: true,
                simulateTyping: true,
                typingDelay: 50
            },
            {
                type: "wait",
                duration: 300,
                description: "Menunggu setelah input password"
            },
            {
                type: "click",
                selector: "#btnLogin",
                selectorType: "css",
                value: "LOG IN",
                description: "Klik tombol login untuk melakukan autentikasi",
                waitAfterClick: 1000
            },
            {
                type: "wait",
                duration: 2000,
                description: "Menunggu proses login selesai"
            },
            {
                type: "popup_handler",
                description: "Menangani popup Location Setting yang muncul setelah login",
                timeout: 10000,
                popupSelectors: [
                    ".PopupBoxLogin",
                    "[class*='popup']",
                    "[class*='modal']"
                ],
                okButtonSelectors: [
                    "#MainContent_btnOkay",
                    ".button",
                    "input[type='button'][value*='OK']",
                    "input[type='button'][value*='Ok']"
                ],
                popupStabilizeDelay: 1000,
                dismissalTimeout: 5000
            },
            {
                type: "wait_for_page_stability",
                description: "Menunggu halaman stabil setelah popup ditutup",
                timeout: 3000,
                maxWait: 10000
            },
            {
                type: "navigate",
                url: "http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterList.aspx",
                description: "Navigasi ke halaman Task Register List setelah popup ditangani",
                ensurePageReady: true,
                stabilityDelay: 2000,
                timeout: 30000
            }
        ];

        // Predefined Post-Login flow (assumes already logged in)
        this.postLoginFlow = [
            {
                type: "navigate",
                url: "http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterDet.aspx",
                description: "Navigasi langsung ke halaman Task Register Input",
                ensurePageReady: true,
                stabilityDelay: 1000,
                timeout: 15000
            },
          ];

        // For backward compatibility, keep timesheetLoginFlow pointing to preLoginFlow
        this.timesheetLoginFlow = this.preLoginFlow;
        
        this.init();
    }

    init() {
        console.log('Initializing AutomationBotPopup...');

        this.setupEventListeners();
        this.loadSavedData();
        this.updateStatus('Ready');
        this.setupMessageListener();
        this.initializeWithDefaultConfig();
        this.checkAuthStatus();

        // Initialize flow type UI
        this.updateFlowTypeUI();

        // Initialize flow list rendering
        this.renderFlowEvents();

        console.log('AutomationBotPopup initialization complete');
    }

    async initializeWithDefaultConfig() {
        try {
            const result = await chrome.storage.local.get(['automationConfig']);
            if (!result.automationConfig || !result.automationConfig.apiBaseUrl) {
                await chrome.storage.local.set({ automationConfig: this.defaultConfig });
                this.loadConfigurationToUI();
                this.showNotification('🔧 Configuration initialized for timesheet automation', 'success');
            }
        } catch (error) {
            console.error('Failed to initialize configuration:', error);
        }
    }

    async checkAuthStatus() {
        // For timesheet automation, we don't need API auth initially
        // The credentials are used for the target website login
        this.updateStatus('Ready for timesheet automation');
        this.showNotification('✅ Ready to automate timesheet login', 'success');
    }

    loadConfigurationToUI() {
        document.getElementById('targetUrl').value = this.defaultConfig.targetUrl;
        document.getElementById('username').value = this.defaultConfig.username;
        document.getElementById('password').value = this.defaultConfig.password;
        document.getElementById('scriptUrl').value = this.defaultConfig.apiBaseUrl;
        document.getElementById('sheetName').value = this.defaultConfig.apiKey;
        document.getElementById('delayInterval').value = this.defaultConfig.delayInterval;
    }

    setupEventListeners() {
        // Tab navigation
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // Quick Run Automation
        document.getElementById('runTimesheetAutomation').addEventListener('click', () => {
            this.runTimesheetAutomation();
        });

        // Post-Login Automation
        document.getElementById('runPostLoginAutomation').addEventListener('click', () => {
            this.runPostLoginAutomation();
        });

        // Configuration tab
        document.getElementById('saveConfig').addEventListener('click', () => {
            this.saveConfiguration();
        });

        document.getElementById('testConnection').addEventListener('click', () => {
            this.testApiConnection();
        });

        document.getElementById('debugConnection').addEventListener('click', () => {
            this.debugConnection();
        });

        document.getElementById('copyDebugInfo').addEventListener('click', () => {
            this.copyDebugInfo();
        });

        // Data preview tab
        document.getElementById('fetchData').addEventListener('click', () => {
            this.fetchStagingData();
        });

        document.getElementById('refreshData').addEventListener('click', () => {
            this.refreshStagingData();
        });

        // Flow definition tab
        document.getElementById('addEvent').addEventListener('click', () => {
            this.addFlowEvent();
        });

        document.getElementById('saveFlow').addEventListener('click', () => {
            this.saveFlow();
        });

        document.getElementById('loadFlow').addEventListener('click', () => {
            this.loadFlow();
        });

        document.getElementById('loadPredefinedFlow').addEventListener('click', () => {
            this.loadPredefinedFlow();
        });

        // Flow type management event listeners
        document.getElementById('flowTypePreLogin')?.addEventListener('click', () => {
            this.loadPreLoginFlow();
        });

        document.getElementById('flowTypePostLogin')?.addEventListener('click', () => {
            this.loadPostLoginFlow();
        });

        document.getElementById('flowTypeToggle')?.addEventListener('change', (e) => {
            const flowType = e.target.value;
            this.setFlowType(flowType);
        });

        document.getElementById('startFlow').addEventListener('click', () => {
            this.startAutomationFlow();
        });

        // Execution tab
        document.getElementById('runExecution').addEventListener('click', () => {
            this.runAutomationExecution();
        });

        document.getElementById('pauseExecution').addEventListener('click', () => {
            this.pauseExecution();
        });

        document.getElementById('stopExecution').addEventListener('click', () => {
            this.stopExecution();
        });

        // Enhanced Flow Management
        document.getElementById('saveFlowToFile')?.addEventListener('click', () => {
            console.log('Save Flow to File clicked');
            this.saveFlowToFile();
        });

        document.getElementById('loadFlowFromFile')?.addEventListener('click', () => {
            console.log('Load Flow from File clicked');
            this.loadFlowFromFile();
        });

        document.getElementById('deleteFlowFile')?.addEventListener('click', () => {
            console.log('Delete Flow File clicked');
            this.deleteFlowCompletely();
        });

        document.getElementById('validateFlow')?.addEventListener('click', () => {
            console.log('Validate Flow clicked');
            this.validateCurrentFlow();
        });

        document.getElementById('dryRunFlow')?.addEventListener('click', () => {
            console.log('Dry Run Flow clicked');
            this.performDryRun();
        });

        document.getElementById('preflightCheck')?.addEventListener('click', () => {
            this.performPreflightCheck();
        });

        document.getElementById('testAllEvents')?.addEventListener('click', () => {
            this.testAllEvents();
        });

        // Text Search tab
        document.getElementById('performSearch').addEventListener('click', () => {
            this.performTextSearch();
        });

        document.getElementById('clearSearch').addEventListener('click', () => {
            this.clearTextSearch();
        });

        document.getElementById('showSearchUI').addEventListener('click', () => {
            this.showTextSearchUI();
        });

        document.getElementById('nextMatch').addEventListener('click', () => {
            this.navigateTextSearch('next');
        });

        document.getElementById('previousMatch').addEventListener('click', () => {
            this.navigateTextSearch('previous');
        });
    }

    setupMessageListener() {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true;
        });
    }

    handleMessage(message, sender, sendResponse) {
        switch (message.action) {
            case 'executionProgress':
                this.updateExecutionStatus('Running...', message.progress);
                this.logExecution(`Progress: ${message.progress}% - ${message.step || 'Processing'}`, 'info');
                break;
            
            case 'executionComplete':
                this.isExecuting = false;
                if (message.success) {
                    this.updateExecutionStatus('Completed', 100);
                    this.updateStatus('Automation completed');
                    this.logExecution('✅ Timesheet automation completed successfully', 'success');
                    this.showNotification('🎉 Timesheet automation completed!', 'success');
                    
                    if (message.results) {
                        this.submitAutomationResults(message.results);
                    }
                } else {
                    this.updateExecutionStatus('Failed', 0);
                    this.updateStatus('Automation failed');
                    this.logExecution('❌ Timesheet automation failed: ' + (message.error || 'Unknown error'), 'error');
                    this.showNotification('❌ Automation failed: ' + (message.error || 'Unknown error'), 'error');
                }
                break;

            case 'elementInteraction':
                this.logExecution(`🎯 ${message.type}: ${message.selector} - ${message.status}`, 
                    message.success ? 'success' : 'warning');
                break;

            case 'dataExtracted':
                this.logExecution(`📊 Data extracted: ${message.dataPoints} points`, 'info');
                break;
                
            default:
                break;
        }
        
        sendResponse({ success: true });
    }

    switchTab(tabName) {
        document.querySelectorAll('.tab-button').forEach(button => {
            button.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');

        this.currentTab = tabName;
    }

    async saveConfiguration() {
        const config = {
            apiBaseUrl: document.getElementById('scriptUrl').value,
            targetUrl: document.getElementById('targetUrl').value,
            username: document.getElementById('username').value,
            password: document.getElementById('password').value,
            apiKey: document.getElementById('sheetName').value,
            refreshInterval: 30000,
            retryAttempts: 3,
            delayInterval: parseInt(document.getElementById('delayInterval').value) || 1000
        };

        try {
            await chrome.storage.local.set({ automationConfig: config });
            
            await this.apiService.saveConfiguration({
                baseUrl: config.apiBaseUrl
            });
            
            this.showNotification('✅ Configuration saved successfully', 'success');
            this.logExecution('Configuration updated', 'info');
        } catch (error) {
            this.showNotification('❌ Failed to save configuration: ' + error.message, 'error');
            console.error('Save config error:', error);
        }
    }

    async loadSavedData() {
        try {
            const result = await chrome.storage.local.get(['automationConfig', 'automationFlowEvents']);
            
            if (result.automationConfig) {
                const config = result.automationConfig;
                document.getElementById('scriptUrl').value = config.apiBaseUrl || this.defaultConfig.apiBaseUrl;
                document.getElementById('targetUrl').value = config.targetUrl || this.defaultConfig.targetUrl;
                document.getElementById('username').value = config.username || this.defaultConfig.username;
                document.getElementById('password').value = config.password || this.defaultConfig.password;
                document.getElementById('sheetName').value = config.apiKey || '';
                document.getElementById('delayInterval').value = config.delayInterval || this.defaultConfig.delayInterval;
            }

            if (result.automationFlowEvents) {
                this.flowEvents = result.automationFlowEvents;
                this.renderFlowEvents();
            }
        } catch (error) {
            console.error('Load saved data error:', error);
        }
    }

    async testApiConnection() {
        this.updateStatus('Testing API connection...');
        this.showNotification('🔄 Testing connection to staging API...', 'info');
        
        try {
            const config = await this.getConfiguration();
            
            await this.apiService.saveConfiguration({
                baseUrl: config.apiBaseUrl
            });
            
            const result = await this.apiService.testConnection();
            
            if (result.success) {
                this.showNotification('✅ Staging API connection successful!', 'success');
                this.updateStatus('API connected');
                this.logExecution(`Staging API connection test successful`, 'success');
                return result;
            } else {
                throw new Error(result.error || 'Connection test failed');
            }
            
        } catch (error) {
            console.error('❌ API connection test error:', error);
            this.handleConnectionError(error);
            throw error;
        }
    }

    async runTimesheetAutomation() {
        if (this.isExecuting) {
            this.showNotification('⚠️ Automation already in progress.', 'warning');
            return;
        }

        this.isExecuting = true;
        this.updateStatus('Starting timesheet automation...');
        this.logExecution('🚀 Starting complete timesheet automation flow', 'info');

        try {
            // Step 1: Fetch staging data first
            this.logExecution('📊 Step 1: Fetching timesheet data from staging API', 'info');
            await this.fetchStagingData();

            if (!this.automationData || this.automationData.length === 0) {
                this.showNotification('⚠️ No staging data available. Continuing with login only.', 'warning');
            }

            // Step 2: Get current tab or open new one
            const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });
            const config = await this.getConfiguration();
            
            if (!currentTab) {
                throw new Error('No active tab found');
            }

            // Step 3: Navigate to target website
            this.logExecution('🌐 Step 2: Navigating to timesheet website', 'info');
            await chrome.tabs.update(currentTab.id, { url: config.targetUrl });
            
            await this.waitForPageLoad(currentTab.id);
            this.logExecution('✅ Page loaded successfully', 'success');

            // Step 4: Execute login flow
            this.logExecution('🔐 Step 3: Executing login automation', 'info');
            
            const loginFlow = this.timesheetLoginFlow.map(event => ({
                ...event,
                // Update values from configuration
                value: event.type === 'input' && event.selector.includes('text') ? config.username :
                       event.type === 'input' && event.selector.includes('password') ? config.password :
                       event.value
            }));

            const message = {
                action: 'executeAutomationFlow',
                flowEvents: loginFlow,
                automationData: this.automationData,
                config: config,
                metadata: {
                    startTime: new Date().toISOString(),
                    totalEvents: loginFlow.length,
                    executionId: this.generateExecutionId(),
                    automationType: 'timesheet_login'
                }
            };

            const response = await chrome.tabs.sendMessage(currentTab.id, message);
            
            if (response && response.success === false) {
                throw new Error(response.error || 'Login automation failed');
            }

            this.showNotification('🚀 Timesheet automation started successfully!', 'success');
            
        } catch (error) {
            this.isExecuting = false;
            this.updateStatus('Automation failed');
            this.showNotification(`❌ Timesheet automation failed: ${error.message}`, 'error');
            this.logExecution(`❌ Timesheet automation failed: ${error.message}`, 'error');
        }
    }

    async runPostLoginAutomation() {
        if (this.isExecuting) {
            this.showNotification('⚠️ Automation already in progress.', 'warning');
            return;
        }

        this.isExecuting = true;
        this.updateStatus('Starting post-login automation...');
        this.logExecution('🔄 Starting post-login automation sequence', 'info');

        try {
            // Get current tab
            const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!currentTab) {
                throw new Error('No active tab found');
            }

            // Load the post-login flow definition
            const postLoginFlow = await this.loadPostLoginFlow();

            // Execute the post-login automation sequence
            const message = {
                action: 'executePostLoginSequence',
                config: {
                    pageLoadTimeout: 10000,
                    okElementTimeout: 5000,
                    okElementDelay: 200,
                    targetUrl: 'http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterList.aspx',
                    navigationTimeout: 15000,
                    navigationDelay: 300,
                    newElementTimeout: 10000,
                    newElementWaitAfter: 2000
                },
                metadata: {
                    startTime: new Date().toISOString(),
                    executionId: this.generateExecutionId(),
                    automationType: 'post_login_sequence'
                }
            };

            const response = await chrome.tabs.sendMessage(currentTab.id, message);

            if (response && response.success === false) {
                throw new Error(response.error || 'Post-login automation failed');
            }

            this.showNotification('🔄 Post-login automation started successfully!', 'success');

        } catch (error) {
            this.isExecuting = false;
            this.updateStatus('Post-login automation failed');
            this.showNotification(`❌ Post-login automation failed: ${error.message}`, 'error');
            this.logExecution(`❌ Post-login automation failed: ${error.message}`, 'error');
        }
    }

    async loadPostLoginFlow() {
        // Return the post-login flow configuration
        return {
            flowId: 'post_login_automation_v1',
            name: 'Post-Login Automation Flow',
            steps: [
                { type: 'wait_for_page_load', timeout: 10000 },
                { type: 'search_and_click_ok', searchText: 'ok', timeout: 5000, delay: 200 },
                { type: 'navigate_to_target', url: 'http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterList.aspx' },
                { type: 'wait_for_navigation', timeout: 15000 },
                { type: 'delay', duration: 300 },
                { type: 'search_and_click_new', searchText: 'new', timeout: 10000 },
                { type: 'wait_for_result', duration: 2000 },
                { type: 'initialize_text_search' }
            ]
        };
    }

    // ================== TEXT SEARCH FUNCTIONALITY ==================

    async performTextSearch() {
        try {
            const searchText = document.getElementById('searchText').value;
            const caseSensitive = document.getElementById('caseSensitive').checked;
            const highlightMatches = document.getElementById('highlightMatches').checked;

            if (!searchText.trim()) {
                this.showNotification('⚠️ Please enter search text', 'warning');
                return;
            }

            // Get current tab
            const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!currentTab) {
                throw new Error('No active tab found');
            }

            // Send text search message to content script
            const message = {
                action: 'executeTextSearch',
                searchText: searchText,
                caseSensitive: caseSensitive,
                highlightMatches: highlightMatches,
                showNavigationControls: true,
                maxMatches: 100
            };

            const response = await chrome.tabs.sendMessage(currentTab.id, message);

            if (response && response.success) {
                const results = response.results;
                this.updateSearchResults(results);
                this.showNotification(`🔍 Found ${results.totalMatches} matches`, 'success');
            } else {
                throw new Error(response?.error || 'Text search failed');
            }

        } catch (error) {
            this.showNotification(`❌ Text search failed: ${error.message}`, 'error');
            console.error('Text search error:', error);
        }
    }

    async clearTextSearch() {
        try {
            // Clear search input
            document.getElementById('searchText').value = '';

            // Hide search navigation
            document.getElementById('searchNavigation').style.display = 'none';

            // Get current tab
            const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (currentTab) {
                // Send clear search message to content script
                const message = {
                    action: 'executeTextSearch',
                    searchText: '',
                    caseSensitive: false,
                    highlightMatches: false
                };

                await chrome.tabs.sendMessage(currentTab.id, message);
            }

            this.showNotification('🧹 Search cleared', 'success');

        } catch (error) {
            console.error('Clear search error:', error);
        }
    }

    async showTextSearchUI() {
        try {
            // Get current tab
            const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!currentTab) {
                throw new Error('No active tab found');
            }

            // Send message to show text search UI
            const message = {
                action: 'showTextSearchUI'
            };

            await chrome.tabs.sendMessage(currentTab.id, message);
            this.showNotification('🔍 Text search UI opened', 'success');

        } catch (error) {
            this.showNotification(`❌ Failed to show search UI: ${error.message}`, 'error');
            console.error('Show search UI error:', error);
        }
    }

    async navigateTextSearch(direction) {
        try {
            // Get current tab
            const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!currentTab) {
                throw new Error('No active tab found');
            }

            // Send navigation message to content script
            const message = {
                action: 'executeTextSearchNavigate',
                direction: direction // 'next' or 'previous'
            };

            const response = await chrome.tabs.sendMessage(currentTab.id, message);

            if (response && response.success) {
                this.showNotification(`📍 Navigated to ${direction} match`, 'success');
            } else {
                throw new Error(response?.error || 'Navigation failed');
            }

        } catch (error) {
            this.showNotification(`❌ Navigation failed: ${error.message}`, 'error');
            console.error('Text search navigation error:', error);
        }
    }

    updateSearchResults(results) {
        const navigationDiv = document.getElementById('searchNavigation');
        const resultsInfo = document.getElementById('searchResultsInfo');
        const prevButton = document.getElementById('previousMatch');
        const nextButton = document.getElementById('nextMatch');

        if (results.totalMatches > 0) {
            navigationDiv.style.display = 'block';
            resultsInfo.textContent = `${results.currentIndex + 1} of ${results.totalMatches} matches`;

            // Enable/disable navigation buttons
            prevButton.disabled = results.totalMatches <= 1;
            nextButton.disabled = results.totalMatches <= 1;
        } else {
            navigationDiv.style.display = 'none';
            resultsInfo.textContent = 'No matches found';
        }
    }

    // ================== ENHANCED FLOW MANAGEMENT ==================

    async initializeFlowManager() {
        if (!this.flowManager) {
            try {
                console.log('Initializing FlowManager...');
                
                // Check if FlowManager is available
                if (typeof FlowManager !== 'undefined') {
                    this.flowManager = new FlowManager();
                    console.log('FlowManager initialized successfully');
                } else {
                    console.warn('FlowManager class not available, using fallback methods');
                    this.flowManager = null;
                    
                    // Show user notification about fallback mode
                    this.showNotification('📋 Flow Manager: Using basic file operations', 'info');
                }
            } catch (error) {
                console.error('Failed to initialize FlowManager:', error);
                this.flowManager = null;
                this.showNotification('⚠️ Flow Manager initialization failed, using fallback', 'warning');
            }
        }
        
        return this.flowManager;
    }

    async saveFlowToFile() {
        try {
            await this.initializeFlowManager();

            if (!this.flowEvents || this.flowEvents.length === 0) {
                this.showNotification('⚠️ No flow events to save', 'warning');
                return;
            }

            const flowName = prompt('Enter flow name:');
            if (!flowName) return;

            const description = prompt('Enter flow description (optional):') || '';

            if (this.flowManager) {
                const result = await this.flowManager.saveFlowToFile(flowName, this.flowEvents, description);

                if (result.success) {
                    this.showNotification(`💾 Flow saved to Downloads folder: ${result.filename}`, 'success');
                    this.logExecution(`Flow "${flowName}" saved to file: ${result.filename}`, 'success');
                } else {
                    throw new Error('Failed to save flow to file');
                }
            } else {
                await this.saveFlowToFileBasic(flowName, this.flowEvents, description);
            }

        } catch (error) {
            this.showNotification(`❌ Failed to save flow: ${error.message}`, 'error');
            console.error('Save flow to file error:', error);
        }
    }

    async saveFlowToFileBasic(flowName, flowData, description) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
        const sanitizedName = flowName.replace(/[^a-zA-Z0-9-_]/g, '_');
        const filename = `venus-flow-${sanitizedName}-${timestamp}.json`;

        const flowFileData = {
            metadata: {
                name: flowName,
                description: description,
                created: new Date().toISOString(),
                version: '1.0.0',
                author: 'Venus-Millware AutoFill',
                filename: filename
            },
            flowDefinition: flowData
        };

        const jsonContent = JSON.stringify(flowFileData, null, 2);
        const blob = new Blob([jsonContent], { type: 'application/json' });

        const downloadId = await chrome.downloads.download({
            url: URL.createObjectURL(blob),
            filename: filename,
            saveAs: false
        });

        this.showNotification(`💾 Flow saved to Downloads folder: ${filename}`, 'success');
        console.log(`Flow saved with download ID: ${downloadId}`);
    }

    async loadFlowFromFile() {
        try {
            await this.initializeFlowManager();

            let result;
            if (this.flowManager) {
                result = await this.flowManager.loadFlowFromFile();
            } else {
                result = await this.loadFlowFromFileBasic();
            }

            if (result && result.success) {
                this.flowEvents = result.flowData;
                this.currentFlowData = result;

                // Force re-render of flow events
                this.renderFlowEvents();
                await this.saveFlowEvents();

                this.showNotification(`📂 Flow "${result.flowName}" loaded successfully`, 'success');
                this.logExecution(`Flow loaded from file: ${result.filename || 'unknown'}`, 'success');

                this.updateFlowInfo(result);

                if (result.validation && !result.validation.isValid) {
                    this.showNotification(`⚠️ Flow has validation warnings: ${result.validation.errors.length} issues`, 'warning');
                }
            } else if (result) {
                // Handle case where flowManager returns data but not in expected format
                const flowData = result.flowDefinition || result.events || result;
                if (Array.isArray(flowData)) {
                    this.flowEvents = flowData;
                    this.renderFlowEvents();
                    await this.saveFlowEvents();
                    this.showNotification('📂 Flow loaded successfully', 'success');
                } else {
                    throw new Error('Invalid flow data format');
                }
            }

        } catch (error) {
            if (error.message !== 'File selection cancelled' && error.name !== 'AbortError') {
                this.showNotification(`❌ Failed to load flow: ${error.message}`, 'error');
                console.error('Load flow from file error:', error);
            }
        }
    }

    async loadFlowFromFileBasic() {
        return new Promise((resolve, reject) => {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.style.display = 'none';

            input.onchange = async (event) => {
                try {
                    const file = event.target.files[0];
                    if (!file) {
                        reject(new Error('No file selected'));
                        return;
                    }

                    console.log('Loading flow file:', file.name);
                    const content = await file.text();
                    console.log('File content:', content.substring(0, 200) + '...');
                    
                    const flowData = JSON.parse(content);
                    console.log('Parsed flow data:', flowData);

                    // Handle different JSON formats
                    let flowDefinition;
                    let metadata = {};

                    if (Array.isArray(flowData)) {
                        // Direct array of flow events
                        flowDefinition = flowData;
                        metadata = {
                            name: file.name.replace('.json', ''),
                            created: new Date().toISOString(),
                            source: 'imported_array'
                        };
                    } else if (flowData.flowDefinition) {
                        // Structured format with flowDefinition property
                        flowDefinition = flowData.flowDefinition;
                        metadata = flowData.metadata || {
                            name: flowData.name || file.name.replace('.json', ''),
                            created: flowData.created || new Date().toISOString(),
                            source: 'imported_structured'
                        };
                    } else if (flowData.events) {
                        // Format with events property
                        flowDefinition = flowData.events;
                        metadata = {
                            name: flowData.name || file.name.replace('.json', ''),
                            created: flowData.created || new Date().toISOString(),
                            source: 'imported_events'
                        };
                    } else if (flowData.steps) {
                        // Format with steps property (millware-login-automation.json format)
                        flowDefinition = flowData.steps;
                        metadata = {
                            name: flowData.name || flowData.flowId || file.name.replace('.json', ''),
                            created: flowData.created || new Date().toISOString(),
                            source: 'imported_steps',
                            description: flowData.description
                        };
                    } else {
                        // Try to use the whole object as flow definition
                        flowDefinition = [flowData];
                        metadata = {
                            name: file.name.replace('.json', ''),
                            created: new Date().toISOString(),
                            source: 'imported_single'
                        };
                    }

                    console.log('Extracted flow definition:', flowDefinition);
                    console.log('Metadata:', metadata);

                    if (!Array.isArray(flowDefinition)) {
                        throw new Error('Invalid flow definition: must be an array of events');
                    }

                    resolve({
                        success: true,
                        flowName: metadata.name,
                        flowData: flowDefinition,
                        metadata: metadata,
                        filename: file.name
                    });

                } catch (error) {
                    console.error('Error loading flow file:', error);
                    reject(new Error(`Invalid flow file: ${error.message}`));
                } finally {
                    document.body.removeChild(input);
                }
            };

            input.oncancel = () => {
                document.body.removeChild(input);
                reject(new Error('File selection cancelled'));
            };

            document.body.appendChild(input);
            input.click();
        });
    }

    async deleteFlowCompletely() {
        try {
            if (!this.currentFlowData || !this.currentFlowData.flowName) {
                this.showNotification('⚠️ No flow loaded to delete', 'warning');
                return;
            }

            const flowName = this.currentFlowData.flowName;
            const confirmed = confirm(`Are you sure you want to completely delete the flow "${flowName}"?\n\nThis will remove it from both storage and Downloads folder.`);

            if (!confirmed) return;

            await this.initializeFlowManager();

            if (this.flowManager) {
                await this.flowManager.deleteFlowCompletely(flowName);
            } else {
                await this.deleteFlowBasic(flowName);
            }

            this.flowEvents = [];
            this.currentFlowData = null;
            this.renderFlowEvents();
            this.saveFlowEvents();
            this.updateFlowInfo(null);

            this.showNotification(`🗑️ Flow "${flowName}" deleted completely`, 'success');
            this.logExecution(`Flow "${flowName}" deleted completely`, 'info');

        } catch (error) {
            this.showNotification(`❌ Failed to delete flow: ${error.message}`, 'error');
            console.error('Delete flow error:', error);
        }
    }

    async deleteFlowBasic(flowName) {
        const result = await chrome.storage.local.get(['automationFlows']);
        const flows = result.automationFlows || {};

        if (flows[flowName]) {
            delete flows[flowName];
            await chrome.storage.local.set({ automationFlows: flows });
        }

        try {
            const downloads = await chrome.downloads.search({
                filenameRegex: `venus-flow-${flowName.replace(/[^a-zA-Z0-9-_]/g, '_')}.*\\.json`,
                state: 'complete'
            });

            for (const download of downloads) {
                try {
                    await chrome.downloads.removeFile(download.id);
                } catch (error) {
                    await chrome.downloads.erase({ id: download.id });
                }
            }
        } catch (error) {
            console.warn('Could not delete flow files:', error);
        }
    }

    async validateCurrentFlow() {
        try {
            if (!this.flowEvents || this.flowEvents.length === 0) {
                this.showNotification('⚠️ No flow events to validate', 'warning');
                return;
            }

            await this.initializeFlowManager();

            let validation;
            if (this.flowManager) {
                validation = await this.flowManager.validateFlow(this.flowEvents);
            } else {
                validation = await this.validateFlowBasic(this.flowEvents);
            }

            this.updateValidationStatus(validation);
            this.showValidationResults(validation);

            const message = validation.isValid ?
                '✅ Flow validation passed' :
                `❌ Flow validation failed: ${validation.errors.length} errors`;

            this.showNotification(message, validation.isValid ? 'success' : 'error');
            this.logExecution(`Flow validation: ${validation.isValid ? 'PASSED' : 'FAILED'}`, validation.isValid ? 'success' : 'error');

        } catch (error) {
            this.showNotification(`❌ Validation failed: ${error.message}`, 'error');
            console.error('Flow validation error:', error);
        }
    }

    async validateFlowBasic(flowEvents) {
        const validation = {
            isValid: true,
            errors: [],
            warnings: []
        };

        for (let i = 0; i < flowEvents.length; i++) {
            const event = flowEvents[i];

            if (!event.type) {
                validation.isValid = false;
                validation.errors.push(`Event ${i + 1}: Missing event type`);
                continue;
            }

            switch (event.type) {
                case 'click':
                case 'input':
                    if (!event.selector && !event.searchText) {
                        validation.warnings.push(`Event ${i + 1}: No selector or search text specified`);
                    }
                    break;
                case 'navigate':
                case 'open_to':
                    if (!event.url) {
                        validation.isValid = false;
                        validation.errors.push(`Event ${i + 1}: No URL specified`);
                    }
                    break;
                case 'wait':
                    if (!event.duration && !event.waitFor) {
                        validation.warnings.push(`Event ${i + 1}: No duration or wait condition specified`);
                    }
                    break;
            }
        }

        return validation;
    }

    async performDryRun() {
        try {
            if (!this.flowEvents || this.flowEvents.length === 0) {
                this.showNotification('⚠️ No flow events to test', 'warning');
                return;
            }

            this.showNotification('🧪 Starting dry run simulation...', 'info');
            this.logExecution('Starting dry run simulation', 'info');

            await this.initializeFlowManager();

            let dryRunResult;
            if (this.flowManager) {
                dryRunResult = await this.flowManager.performDryRun(this.flowEvents);
            } else {
                dryRunResult = await this.performDryRunBasic(this.flowEvents);
            }

            this.showDryRunResults(dryRunResult);
            this.showNotification(`🧪 Dry run completed: ${dryRunResult.simulatedEvents}/${dryRunResult.totalEvents} events simulated`, 'success');

        } catch (error) {
            this.showNotification(`❌ Dry run failed: ${error.message}`, 'error');
            console.error('Dry run error:', error);
        }
    }

    async performDryRunBasic(flowEvents) {
        const result = {
            totalEvents: flowEvents.length,
            simulatedEvents: 0,
            skippedEvents: 0,
            estimatedDuration: 0,
            eventResults: []
        };

        for (let i = 0; i < flowEvents.length; i++) {
            const event = flowEvents[i];
            const eventResult = {
                eventName: event.name || `Event ${i + 1}`,
                eventType: event.type,
                status: 'simulated',
                notes: ''
            };

            switch (event.type) {
                case 'click':
                    eventResult.notes = event.selector ?
                        `Will click element: ${event.selector}` :
                        'No selector specified';
                    result.simulatedEvents++;
                    result.estimatedDuration += 500;
                    break;
                case 'input':
                    eventResult.notes = event.selector ?
                        `Will input "${event.value || 'value'}" to: ${event.selector}` :
                        'No selector specified';
                    result.simulatedEvents++;
                    result.estimatedDuration += 300;
                    break;
                case 'wait':
                    const duration = event.duration || 1000;
                    eventResult.notes = `Will wait ${duration}ms`;
                    result.simulatedEvents++;
                    result.estimatedDuration += duration;
                    break;
                case 'navigate':
                    eventResult.notes = event.url ?
                        `Will navigate to: ${event.url}` :
                        'No URL specified';
                    result.simulatedEvents++;
                    result.estimatedDuration += 3000;
                    break;
                default:
                    eventResult.status = 'skipped';
                    eventResult.notes = 'Event type not simulated in basic dry run';
                    result.skippedEvents++;
            }

            result.eventResults.push(eventResult);
        }

        return result;
    }

    async performPreflightCheck() {
        try {
            if (!this.flowEvents || this.flowEvents.length === 0) {
                this.showNotification('⚠️ No flow events to check', 'warning');
                return;
            }

            this.showNotification('🔍 Performing preflight check...', 'info');

            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            const response = await chrome.tabs.sendMessage(tab.id, {
                action: 'preflightCheck',
                flowEvents: this.flowEvents
            });

            if (response.success) {
                this.showPreflightResults(response.results);
                const message = `🔍 Preflight check completed: ${response.results.elementsFound}/${response.results.totalElements} elements found`;
                this.showNotification(message, response.results.elementsFound === response.results.totalElements ? 'success' : 'warning');
            } else {
                throw new Error(response.error || 'Preflight check failed');
            }

        } catch (error) {
            this.showNotification(`❌ Preflight check failed: ${error.message}`, 'error');
            console.error('Preflight check error:', error);
        }
    }

    updateValidationStatus(validation) {
        const statusElement = document.getElementById('validationStatus');
        if (statusElement) {
            const statusText = statusElement.querySelector('.status-text');
            if (validation.isValid) {
                statusText.textContent = `✅ Valid (${validation.warnings.length} warnings)`;
                statusText.style.color = '#28a745';
            } else {
                statusText.textContent = `❌ Invalid (${validation.errors.length} errors)`;
                statusText.style.color = '#dc3545';
            }
        }
    }

    updateFlowInfo(flowData) {
        const flowInfoDiv = document.getElementById('flowInfo');
        if (!flowInfoDiv) return;

        if (!flowData) {
            flowInfoDiv.style.display = 'none';
            return;
        }

        flowInfoDiv.style.display = 'block';

        document.getElementById('flowName').textContent = flowData.flowName || 'Unknown';
        document.getElementById('flowEventCount').textContent = this.flowEvents.length;
        document.getElementById('flowStatus').textContent = 'Loaded';
        document.getElementById('flowLastModified').textContent =
            flowData.metadata?.created ? new Date(flowData.metadata.created).toLocaleString() : 'Unknown';
    }

    showValidationResults(validation) {
        const resultsHTML = `
            <div class="validation-results">
                <h4>Flow Validation Results</h4>
                <div class="validation-status ${validation.isValid ? 'valid' : 'invalid'}">
                    ${validation.isValid ? '✅ Flow is valid' : '❌ Flow has errors'}
                </div>

                ${validation.errors.length > 0 ? `
                    <div class="validation-errors">
                        <h5>Errors (${validation.errors.length}):</h5>
                        <ul>${validation.errors.map(error => `<li>${error}</li>`).join('')}</ul>
                    </div>
                ` : ''}

                ${validation.warnings.length > 0 ? `
                    <div class="validation-warnings">
                        <h5>Warnings (${validation.warnings.length}):</h5>
                        <ul>${validation.warnings.map(warning => `<li>${warning}</li>`).join('')}</ul>
                    </div>
                ` : ''}
            </div>
        `;

        this.showModal('Flow Validation Results', resultsHTML);
    }

    showDryRunResults(dryRunResult) {
        const resultsHTML = `
            <div class="dry-run-results">
                <h4>Dry Run Simulation Results</h4>
                <div class="summary">
                    <p><strong>Total Events:</strong> ${dryRunResult.totalEvents}</p>
                    <p><strong>Simulated:</strong> ${dryRunResult.simulatedEvents}</p>
                    <p><strong>Skipped:</strong> ${dryRunResult.skippedEvents}</p>
                    <p><strong>Estimated Duration:</strong> ${(dryRunResult.estimatedDuration / 1000).toFixed(1)}s</p>
                </div>

                <div class="event-results">
                    <h5>Event Simulation Details:</h5>
                    ${dryRunResult.eventResults.map((result, index) => `
                        <div class="event-result ${result.status}">
                            <div class="event-header">
                                <span class="event-name">${result.eventName}</span>
                                <span class="event-type">${result.eventType}</span>
                                <span class="event-status">${result.status}</span>
                            </div>
                            ${result.notes ? `<div class="event-notes">${result.notes}</div>` : ''}
                        </div>
                    `).join('')}
                </div>
            </div>
        `;

        this.showModal('Dry Run Results', resultsHTML);
    }

    showPreflightResults(results) {
        const resultsHTML = `
            <div class="preflight-results">
                <h4>Preflight Check Results</h4>
                <div class="summary">
                    <p><strong>Elements Found:</strong> ${results.elementsFound}/${results.totalElements}</p>
                    <p><strong>Success Rate:</strong> ${((results.elementsFound / results.totalElements) * 100).toFixed(1)}%</p>
                </div>

                ${results.missingElements.length > 0 ? `
                    <div class="missing-elements">
                        <h5>Missing Elements:</h5>
                        <ul>${results.missingElements.map(selector => `<li>${selector}</li>`).join('')}</ul>
                    </div>
                ` : ''}

                ${results.foundElements ? `
                    <div class="found-elements">
                        <h5>Found Elements:</h5>
                        <ul>${results.foundElements.map(selector => `<li>${selector}</li>`).join('')}</ul>
                    </div>
                ` : ''}
            </div>
        `;

        this.showModal('Preflight Check Results', resultsHTML);
    }

    showModal(title, content) {
        // Remove existing modal if any
        const existingModal = document.querySelector('.modal-overlay');
        if (existingModal) {
            document.body.removeChild(existingModal);
        }

        // Create modal
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>${title}</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary modal-close">Close</button>
                </div>
            </div>
        `;

        // Add styles
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        `;

        const modalContent = modal.querySelector('.modal-content');
        modalContent.style.cssText = `
            background: white;
            border-radius: 8px;
            padding: 20px;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        `;

        document.body.appendChild(modal);

        // Close modal handlers
        modal.querySelectorAll('.modal-close').forEach(btn => {
            btn.addEventListener('click', () => {
                document.body.removeChild(modal);
            });
        });

        // Close on backdrop click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }

    async fetchStagingData() {
        this.updateStatus('Fetching timesheet data...');
        document.getElementById('dataStatus').innerHTML = '<span>🔄 Loading timesheet data from staging API...</span>';
        
        try {
            this.logExecution('Fetching data from staging API endpoint', 'info');
            
            const response = await this.apiService.fetchStagingData();
            
            if (response.success) {
                this.automationData = response.data;
                this.renderTimesheetDataPreview(response);
                this.updateStatus('Timesheet data loaded');
                
                const statusMessage = `✅ Loaded ${this.automationData.length} timesheet records`;
                document.getElementById('dataStatus').innerHTML = `<span>${statusMessage}</span>`;
                
                this.showNotification(`Successfully loaded ${this.automationData.length} timesheet records`, 'success');
                this.logExecution(`Staging data fetch successful: ${this.automationData.length} records`, 'success');
                
                return response;
            } else {
                throw new Error(response.error || 'Failed to fetch timesheet data');
            }
            
        } catch (error) {
            console.error('❌ Staging data fetch error:', error);
            this.handleDataFetchError(error);
            throw error;
        }
    }

    async refreshStagingData() {
        await this.fetchStagingData();
    }

    renderTimesheetDataPreview(response) {
        const previewContainer = document.getElementById('dataPreview');
        
        if (!this.automationData || this.automationData.length === 0) {
            previewContainer.innerHTML = '<p>No timesheet data available</p>';
            return;
        }

        const table = document.createElement('table');
        table.className = 'data-table';
        
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');
        
        // Define timesheet-specific headers
        const headers = [
            { key: 'employeeId', label: 'Employee ID' },
            { key: 'employeeName', label: 'Employee Name' },
            { key: 'date', label: 'Date' },
            { key: 'checkIn', label: 'Check In' },
            { key: 'checkOut', label: 'Check Out' },
            { key: 'regularHours', label: 'Regular Hours' },
            { key: 'overtimeHours', label: 'Overtime Hours' },
            { key: 'totalHours', label: 'Total Hours' }
        ];
        
        headers.forEach(header => {
            const th = document.createElement('th');
            th.textContent = header.label;
            headerRow.appendChild(th);
        });
        
        thead.appendChild(headerRow);
        table.appendChild(thead);
        
        const tbody = document.createElement('tbody');
        
        this.automationData.slice(0, 10).forEach(record => {
            const tr = document.createElement('tr');
            
            headers.forEach(header => {
                const td = document.createElement('td');
                const value = record[header.key];
                
                if (typeof value === 'number') {
                    td.textContent = value.toFixed(2);
                } else {
                    td.textContent = String(value || '');
                }
                
                tr.appendChild(td);
            });
            
            tbody.appendChild(tr);
        });
        
        table.appendChild(tbody);
        previewContainer.innerHTML = '';
        previewContainer.appendChild(table);
        
        if (this.automationData.length > 10) {
            const moreInfo = document.createElement('p');
            moreInfo.textContent = `... and ${this.automationData.length - 10} more timesheet records`;
            moreInfo.style.textAlign = 'center';
            moreInfo.style.color = '#6c757d';
            moreInfo.style.fontSize = '12px';
            moreInfo.style.marginTop = '10px';
            previewContainer.appendChild(moreInfo);
        }

        this.addTimesheetDataSummary(previewContainer, response);
    }

    addTimesheetDataSummary(container, response) {
        const summary = document.createElement('div');
        summary.style.marginTop = '20px';
        summary.style.padding = '15px';
        summary.style.backgroundColor = '#e8f5e8';
        summary.style.borderRadius = '8px';
        summary.style.border = '1px solid #4caf50';
        
        const totalRecords = this.automationData.length;
        const totalRegularHours = this.automationData.reduce((sum, record) => sum + (record.regularHours || 0), 0);
        const totalOvertimeHours = this.automationData.reduce((sum, record) => sum + (record.overtimeHours || 0), 0);
        const lastUpdated = new Date().toLocaleString();
        
        summary.innerHTML = `
            <h4 style="margin: 0 0 10px 0; color: #2e7d32;">📊 Timesheet Data Summary</h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px;">
                <div>
                    <strong>Total Records:</strong><br>
                    <span style="font-size: 18px; color: #2e7d32;">${totalRecords}</span>
                </div>
                <div>
                    <strong>Regular Hours:</strong><br>
                    <span style="font-size: 16px; color: #1976d2;">${totalRegularHours.toFixed(1)}h</span>
                </div>
                <div>
                    <strong>Overtime Hours:</strong><br>
                    <span style="font-size: 16px; color: #f57c00;">${totalOvertimeHours.toFixed(1)}h</span>
                </div>
                <div>
                    <strong>Last Updated:</strong><br>
                    <span style="font-size: 12px; color: #666;">${lastUpdated}</span>
                </div>
            </div>
        `;
        
        container.appendChild(summary);
    }

    // ================== FLOW TYPE MANAGEMENT ==================

    /**
     * Set the current flow type and update UI accordingly
     */
    setFlowType(flowType) {
        this.currentFlowType = flowType;
        this.updateFlowTypeUI();
        this.logExecution(`Flow type changed to: ${flowType}`, 'info');
    }

    /**
     * Get the current flow based on flow type
     */
    getCurrentFlow() {
        return this.currentFlowType === 'pre-login' ? this.preLoginFlow : this.postLoginFlow;
    }

    /**
     * Update UI elements based on current flow type
     */
    updateFlowTypeUI() {
        // Update flow type indicator
        const flowTypeIndicator = document.getElementById('flowTypeIndicator');
        if (flowTypeIndicator) {
            flowTypeIndicator.textContent = this.currentFlowType === 'pre-login' ? 'Pre-Login Flow' : 'Post-Login Flow';
            flowTypeIndicator.className = `flow-type-indicator ${this.currentFlowType}`;
        }

        // Update predefined flow button text
        const loadPredefinedBtn = document.getElementById('loadPredefinedFlow');
        if (loadPredefinedBtn) {
            loadPredefinedBtn.textContent = this.currentFlowType === 'pre-login' ?
                'Muat Pre-Login Flow' : 'Muat Post-Login Flow';
        }
    }

    /**
     * Load predefined flow based on current flow type
     */
    loadPredefinedFlow() {
        const currentFlow = this.getCurrentFlow();
        this.flowEvents = [...currentFlow];
        this.renderFlowEvents();
        this.saveFlowEvents();

        const flowTypeName = this.currentFlowType === 'pre-login' ? 'Pre-Login' : 'Post-Login';
        this.showNotification(`📂 ${flowTypeName} flow loaded successfully`, 'success');
        this.logExecution(`Loaded predefined ${flowTypeName} automation flow`, 'info');
    }

    /**
     * Legacy method for backward compatibility
     */
    loadPredefinedTimesheetFlow() {
        // Default to pre-login flow for backward compatibility
        this.setFlowType('pre-login');
        this.loadPredefinedFlow();
    }

    /**
     * Load Pre-Login flow specifically
     */
    loadPreLoginFlow() {
        this.setFlowType('pre-login');
        this.loadPredefinedFlow();
    }

    /**
     * Load Post-Login flow specifically
     */
    loadPostLoginFlow() {
        this.setFlowType('post-login');
        this.loadPredefinedFlow();
    }

    renderFlowEvents() {
        const flowList = document.getElementById('flowList');

        if (this.flowEvents.length === 0) {
            flowList.innerHTML = `
                <div class="no-flow-message">
                    <p style="padding: 20px; text-align: center; color: #6c757d;">
                        No automation events defined. Create a new flow or load an existing one.
                    </p>
                </div>
            `;
            return;
        }

        flowList.innerHTML = '';

        this.flowEvents.forEach((event, index) => {
            const eventDiv = document.createElement('div');
            eventDiv.className = 'flow-event';
            eventDiv.setAttribute('data-index', index);

            const summary = this.getEventSummary(event);
            const description = event.description ? ` - ${event.description}` : '';

            eventDiv.innerHTML = `
                <div class="event-header">
                    <div class="event-info">
                        <div class="event-type">${event.type.toUpperCase()} #${index + 1}</div>
                        <div class="event-details">${summary}${description}</div>
                    </div>
                    <div class="event-actions">
                        <button class="btn btn-sm btn-info" onclick="automationBot.testSingleEvent(${index})" title="Test this event">
                            🧪 Test
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="automationBot.editFlowEvent && automationBot.editFlowEvent(${index})" title="Edit event">
                            ✏️ Edit
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="automationBot.deleteFlowEvent(${index})" title="Delete event">
                            🗑️ Delete
                        </button>
                    </div>
                </div>
                <div class="event-status" id="eventStatus${index}">
                    <span class="status-indicator" data-status="pending">●</span>
                    <span class="status-text">Ready for testing</span>
                </div>
            `;

            flowList.appendChild(eventDiv);
        });
    }

    getEventSummary(event) {
        switch (event.type) {
            case 'click':
                return `Click: ${event.selector}${event.value ? ` (${event.value})` : ''}`;
            case 'input':
                return `Input: ${event.selector} = "${event.value || event.dataMapping || 'value'}"`;
            case 'wait':
                return `Wait: ${event.duration}ms`;
            case 'extract':
                return `Extract ${event.attribute} from ${event.selector} → ${event.variableName}`;
            case 'navigate':
                return `Navigate to: ${event.url}`;
            case 'data_extract_multiple':
                return `Extract ${event.extractions} data points`;
            case 'prevent_redirect':
                return `Prevent redirect for ${event.timeout}ms (${event.blockMethods?.join(', ') || 'all methods'})`;
            default:
                return JSON.stringify(event);
        }
    }

    async getConfiguration() {
        try {
            const result = await chrome.storage.local.get(['automationConfig']);
            const config = result.automationConfig || this.defaultConfig;
            
            if (!config.apiBaseUrl) {
                throw new Error('API Base URL is not configured');
            }
            
            return config;
        } catch (error) {
            console.error('Failed to get configuration:', error);
            throw new Error('Configuration error: ' + error.message);
        }
    }

    handleConnectionError(error) {
        let errorMessage = 'API connection error: ' + error.message;
        let debugInfo = '';
        
        if (error.message.includes('timeout')) {
            errorMessage = '⏱️ Connection timeout - Staging API server not responding';
            debugInfo = 'Check if your staging API server is running on localhost:5173';
        } else if (error.message.includes('NetworkError') || error.message.includes('Failed to fetch')) {
            errorMessage = '🌐 Network error - Unable to reach staging API server';
            debugInfo = 'Verify the API URL and ensure the server is running';
        } else if (error.message.includes('HTTP 401')) {
            errorMessage = '🔒 Authentication required';
            debugInfo = 'Please configure valid API credentials';
        } else if (error.message.includes('HTTP 404')) {
            errorMessage = '🔍 API endpoint not found (404)';
            debugInfo = 'Verify the staging API base URL is correct';
        }
        
        this.showNotification(errorMessage, 'error');
        this.updateStatus('API connection failed');
        this.logExecution(`API connection failed: ${errorMessage}`, 'error');
        
        if (debugInfo) {
            this.logExecution(`💡 Tip: ${debugInfo}`, 'warning');
        }
    }

    handleDataFetchError(error) {
        let errorMessage = 'Staging data fetch error: ' + error.message;
        
        this.showNotification(errorMessage, 'error');
        this.updateStatus('Data fetch failed');
        document.getElementById('dataStatus').innerHTML = `<span style="color: #dc3545;">❌ Failed to load timesheet data</span>`;
        this.logExecution(`Staging data fetch failed: ${errorMessage}`, 'error');
    }

    async debugConnection() {
        const debugDiv = document.getElementById('debugInfo');
        const debugOutput = document.getElementById('debugOutput');
        
        debugDiv.style.display = 'block';
        
        const scriptUrl = document.getElementById('scriptUrl').value;
        const sheetName = document.getElementById('sheetName').value;
        
        const debugInfo = {
            timestamp: new Date().toISOString(),
            configuration: {
                apiBaseUrl: scriptUrl,
                apiKey: sheetName,
                urlValid: scriptUrl.includes('http'),
                urlLength: scriptUrl.length,
                targetUrl: document.getElementById('targetUrl').value,
                username: document.getElementById('username').value ? 'configured' : 'missing'
            },
            browser: {
                userAgent: navigator.userAgent,
                language: navigator.language,
                online: navigator.onLine,
                cookieEnabled: navigator.cookieEnabled
            },
            extension: {
                manifestVersion: chrome.runtime.getManifest().version,
                permissions: chrome.runtime.getManifest().permissions,
                hostPermissions: chrome.runtime.getManifest().host_permissions
            },
            networkTest: null
        };
        
        debugOutput.value = JSON.stringify(debugInfo, null, 2);
        
        try {
            this.updateStatus('Running debug tests...');
            
            const result = await this.apiService.testConnection();
            
            debugInfo.networkTest = {
                success: result.success,
                serverInfo: result.serverInfo || {},
                timestamp: result.timestamp,
                error: result.error || null
            };
            
            this.updateStatus('Debug test completed');
            this.showNotification('Debug information collected successfully', 'success');
            
        } catch (error) {
            debugInfo.networkTest = {
                success: false,
                error: error.message,
                errorName: error.name
            };
            
            this.updateStatus('Debug test failed');
            this.showNotification('Debug test failed: ' + error.message, 'error');
        }
        
        debugOutput.value = JSON.stringify(debugInfo, null, 2);
        debugDiv.scrollIntoView({ behavior: 'smooth' });
    }

    copyDebugInfo() {
        const debugOutput = document.getElementById('debugOutput');
        
        try {
            debugOutput.select();
            document.execCommand('copy');
            this.showNotification('Debug information copied to clipboard', 'success');
        } catch (error) {
            navigator.clipboard.writeText(debugOutput.value).then(() => {
                this.showNotification('Debug information copied to clipboard', 'success');
            }).catch(() => {
                this.showNotification('Failed to copy debug information', 'error');
            });
        }
    }

    addFlowEvent() {
        const eventTypes = [
            // Basic events
            'click', 'input', 'wait', 'extract', 'navigate', 'scroll',
            // New navigation events
            'open_to', 'wait_for_element', 
            // UI interaction events
            'hover', 'scroll_to', 'select_option', 'alert_handle', 'screenshot',
            // Form handling
            'form_fill',
            // Advanced flow control
            'loop', 'if_then_else', 'variable_set', 
            // Data extraction
            'data_extract_multiple',
            // Tab management
            'tab_switch',
            // Redirect prevention
            'prevent_redirect'
        ];

        const eventType = prompt(`Event type (${eventTypes.join(', ')}):`);
        if (!eventType) return;

        let eventData = { type: eventType.toLowerCase(), id: Date.now() };
        
        // Add conditional logic option for any event
        const addCondition = confirm('Add conditional logic to this event?');
        if (addCondition) {
            eventData.condition = this.promptForCondition();
        }
        
        switch (eventType.toLowerCase()) {
            case 'click':
                eventData.selector = prompt('Element selector:');
                eventData.selectorType = prompt('Selector type (css, xpath, text):') || 'css';
                if (eventData.selectorType === 'text') {
                    eventData.value = prompt('Button text to find:');
                }
                break;
                
            case 'input':
                eventData.selector = prompt('Input element selector:');
                eventData.value = prompt('Value to input:');
                eventData.dataMapping = prompt('Map from API data (optional):');
                eventData.clearFirst = confirm('Clear field before input?');
                break;
                
            case 'wait':
                eventData.duration = parseInt(prompt('Wait duration (ms):')) || 1000;
                eventData.waitFor = prompt('Wait for (time, element, navigation):') || 'time';
                if (eventData.waitFor === 'element') {
                    eventData.condition = prompt('Element selector to wait for:');
                }
                break;
                
            case 'extract':
                eventData.selector = prompt('Element selector:');
                eventData.attribute = prompt('Attribute to extract (text, value, href):') || 'text';
                eventData.variableName = prompt('Store in variable:');
                break;
                
            case 'navigate':
                eventData.url = prompt('Target URL:');
                eventData.waitForLoad = confirm('Wait for page to load?');
                break;

            // New event types
            case 'open_to':
                eventData.url = prompt('URL to open:');
                eventData.newTab = confirm('Open in new tab?');
                eventData.waitForLoad = confirm('Wait for page to load?');
                break;

            case 'wait_for_element':
                eventData.selector = prompt('Element selector to wait for:');
                eventData.timeout = parseInt(prompt('Timeout (ms):')) || 10000;
                eventData.expectVisible = confirm('Wait for element to be visible? (false = wait for disappear)');
                break;

            case 'hover':
                eventData.selector = prompt('Element selector to hover:');
                eventData.selectorType = prompt('Selector type (css, xpath, text):') || 'css';
                eventData.duration = parseInt(prompt('Hover duration (ms) - optional:')) || null;
                break;

            case 'scroll_to':
                const scrollTarget = prompt('Scroll target (element/position):');
                if (scrollTarget === 'element') {
                    eventData.selector = prompt('Element selector:');
                    eventData.block = prompt('Block position (start, center, end, nearest):') || 'center';
                } else {
                    const x = parseInt(prompt('X position:')) || 0;
                    const y = parseInt(prompt('Y position:')) || 0;
                    eventData.position = { x, y };
                }
                eventData.smooth = confirm('Smooth scrolling?');
                break;

            case 'select_option':
                eventData.selector = prompt('Select element selector:');
                const selectionMethod = prompt('Select by (value, text, index):');
                if (selectionMethod === 'value') {
                    eventData.value = prompt('Option value:');
                } else if (selectionMethod === 'text') {
                    eventData.text = prompt('Option text:');
                } else {
                    eventData.index = parseInt(prompt('Option index:'));
                }
                break;

            case 'alert_handle':
                eventData.action = prompt('Action (accept, dismiss, text):') || 'accept';
                if (eventData.action === 'text') {
                    eventData.text = prompt('Text to enter:');
                }
                eventData.timeout = parseInt(prompt('Timeout (ms):')) || 5000;
                break;

            case 'screenshot':
                eventData.name = prompt('Screenshot name:') || `screenshot_${Date.now()}`;
                break;

            case 'form_fill':
                eventData.fields = this.promptForFormFields();
                eventData.fieldDelay = parseInt(prompt('Delay between fields (ms):')) || 100;
                break;

            case 'tab_switch':
                const switchMethod = prompt('Switch by (index, url):');
                if (switchMethod === 'index') {
                    eventData.tabIndex = parseInt(prompt('Tab index:'));
                } else {
                    eventData.url = prompt('URL to open in new tab:');
                }
                break;

            case 'loop':
                eventData.iterations = parseInt(prompt('Number of iterations:'));
                eventData.events = [];
                eventData.iterationDelay = parseInt(prompt('Delay between iterations (ms):')) || 500;
                eventData.continueOnError = confirm('Continue on error?');
                alert('Loop created. Add sub-events using the visual editor.');
                break;

            case 'if_then_else':
                eventData.condition = this.promptForCondition();
                eventData.thenEvents = [];
                eventData.elseEvents = [];
                alert('Conditional event created. Add then/else events using the visual editor.');
                break;

            case 'variable_set':
                eventData.variableName = prompt('Variable name:');
                const valueSource = prompt('Value source (static, element, expression, data):');
                if (valueSource === 'static') {
                    eventData.value = prompt('Static value:');
                } else if (valueSource === 'element') {
                    eventData.selector = prompt('Element selector:');
                    eventData.attribute = prompt('Attribute (text, value, etc.):') || 'text';
                } else if (valueSource === 'expression') {
                    eventData.expression = prompt('JavaScript expression:');
                } else if (valueSource === 'data') {
                    eventData.dataMapping = prompt('Data path (e.g., employee.name):');
                }
                break;

            case 'data_extract_multiple':
                eventData.extractions = this.promptForMultipleExtractions();
                break;

            case 'prevent_redirect':
                eventData.timeout = parseInt(prompt('Timeout duration (ms):')) || 5000;
                eventData.blockMethods = prompt('Methods to block (comma-separated):').split(',').map(m => m.trim()) || 
                    ["location.href", "location.assign", "location.replace", "meta_refresh"];
                eventData.allowManualNavigation = confirm('Allow manual navigation?');
                break;

            default:
                this.showNotification('Invalid event type', 'error');
                return;
        }
        
        eventData.description = prompt('Event description (optional):') || '';
        
        this.flowEvents.push(eventData);
        this.renderFlowEvents();
        this.saveFlowEvents();
        this.showNotification(`✅ ${eventType} event added successfully`, 'success');
    }

    promptForCondition() {
        const conditionTypes = [
            'element_exists', 'element_text_contains', 'url_contains', 
            'variable_equals', 'page_title_contains', 'element_count', 'custom_script'
        ];
        
        const conditionType = prompt(`Condition type (${conditionTypes.join(', ')}):`);
        if (!conditionType) return null;

        const condition = { type: conditionType };

        switch (conditionType) {
            case 'element_exists':
                condition.selector = prompt('Element selector:');
                condition.visible = confirm('Must be visible?');
                break;
            case 'element_text_contains':
                condition.selector = prompt('Element selector:');
                condition.value = prompt('Text to search for:');
                break;
            case 'url_contains':
                condition.value = prompt('URL part to match:');
                break;
            case 'variable_equals':
                condition.variableName = prompt('Variable name:');
                condition.value = prompt('Expected value:');
                break;
            case 'page_title_contains':
                condition.value = prompt('Title text to match:');
                break;
            case 'element_count':
                condition.selector = prompt('Element selector:');
                condition.operator = prompt('Operator (equals, greater_than, less_than):') || 'greater_than';
                condition.value = parseInt(prompt('Count value:'));
                break;
            case 'custom_script':
                condition.script = prompt('JavaScript expression (return boolean):');
                break;
        }

        return condition;
    }

    promptForFormFields() {
        const fields = [];
        let addMore = true;

        while (addMore) {
            const field = {
                selector: prompt('Field selector:'),
                value: prompt('Field value:'),
                required: confirm('Required field?')
            };

            const hasDataMapping = confirm('Map from API data?');
            if (hasDataMapping) {
                field.dataMapping = prompt('Data mapping path:');
            }

            fields.push(field);
            addMore = confirm('Add another field?');
        }

        return fields;
    }

    promptForMultipleExtractions() {
        const extractions = [];
        let addMore = true;

        while (addMore) {
            const extraction = {
                name: prompt('Extraction name:'),
                selector: prompt('Element selector:'),
                attribute: prompt('Attribute (text, value, href):') || 'text',
                required: confirm('Required extraction?'),
                storeGlobally: confirm('Store globally?')
            };

            const hasTransform = confirm('Apply transformation?');
            if (hasTransform) {
                const transformType = prompt('Transform type (trim, uppercase, lowercase, number, regex_extract):');
                extraction.transform = { type: transformType };
                
                if (transformType === 'regex_extract') {
                    extraction.transform.pattern = prompt('Regex pattern:');
                    extraction.transform.group = parseInt(prompt('Capture group (default 0):')) || 0;
                }
            }

            extractions.push(extraction);
            addMore = confirm('Add another extraction?');
        }

        return extractions;
    }

    async saveFlowEvents() {
        try {
            await chrome.storage.local.set({ automationFlowEvents: this.flowEvents });
        } catch (error) {
            console.error('Save flow events error:', error);
        }
    }

    async startAutomationFlow() {
        if (this.isExecuting) {
            this.showNotification('⚠️ Automation already in progress.', 'warning');
            return;
        }
        
        if (this.flowEvents.length === 0) {
            this.showNotification('❌ No automation events defined.', 'error');
            return;
        }
        
        const config = await this.getConfiguration();
        const targetUrl = config.targetUrl;
        
        if (!targetUrl) {
            this.showNotification('❌ Target URL not configured.', 'error');
            return;
        }
        
        this.isExecuting = true;
        this.updateStatus('Starting automation...');
        this.logExecution(`🚀 Starting automation flow with ${this.flowEvents.length} events`, 'info');
        
        try {
            const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            if (!currentTab) {
                throw new Error('No active tab found');
            }
            
            await chrome.tabs.update(currentTab.id, { url: targetUrl });
            this.logExecution(`📍 Navigating to: ${targetUrl}`, 'info');
            
            await this.waitForPageLoad(currentTab.id);
            this.logExecution('✅ Page loaded successfully', 'success');
            
            const message = {
                action: 'executeAutomationFlow',
                flowEvents: this.flowEvents,
                automationData: this.automationData,
                config: config,
                metadata: {
                    startTime: new Date().toISOString(),
                    totalEvents: this.flowEvents.length,
                    executionId: this.generateExecutionId()
                }
            };
            
            const response = await chrome.tabs.sendMessage(currentTab.id, message);
            
            if (response && response.success === false) {
                throw new Error(response.error || 'Flow execution failed');
            }
            
            this.showNotification('🚀 Automation flow started successfully!', 'success');
            
        } catch (error) {
            this.isExecuting = false;
            this.updateStatus('Automation failed');
            this.showNotification(`❌ Automation failed: ${error.message}`, 'error');
            this.logExecution(`❌ Automation failed: ${error.message}`, 'error');
        }
    }

    async runAutomationExecution() {
        return this.startAutomationFlow();
    }

    generateExecutionId() {
        return 'exec_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    async submitAutomationResults(results) {
        try {
            const submissionData = {
                executionId: results.executionId,
                timestamp: new Date().toISOString(),
                success: results.success,
                eventsExecuted: results.eventsExecuted,
                duration: results.duration,
                processedRecords: results.processedRecords || 0,
                extractedData: results.extractedData || {},
                errors: results.errors || []
            };

            const response = await this.apiService.submitAutomationResults(submissionData);
            
            if (response.success) {
                this.logExecution('✅ Results submitted to staging API successfully', 'success');
            } else {
                this.logExecution(`⚠️ Failed to submit results: ${response.error}`, 'warning');
            }
        } catch (error) {
            this.logExecution(`❌ Error submitting results: ${error.message}`, 'error');
        }
    }

    async waitForPageLoad(tabId, timeout = 30000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            const checkComplete = () => {
                chrome.tabs.get(tabId, (tab) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                        return;
                    }
                    
                    if (tab.status === 'complete') {
                        setTimeout(resolve, 2000);
                    } else if (Date.now() - startTime > timeout) {
                        reject(new Error('Page load timeout'));
                    } else {
                        setTimeout(checkComplete, 100);
                    }
                });
            };
            
            checkComplete();
        });
    }

    pauseExecution() {
        if (!this.isExecuting) return;
        
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            chrome.tabs.sendMessage(tabs[0].id, { action: 'pauseAutomation' });
        });
        
        this.updateExecutionStatus('Paused', null);
        this.logExecution('⏸️ Automation paused', 'warning');
    }

    stopExecution() {
        if (!this.isExecuting) return;
        
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            chrome.tabs.sendMessage(tabs[0].id, { action: 'stopAutomation' });
        });
        
        this.isExecuting = false;
        this.updateExecutionStatus('Stopped', 0);
        this.logExecution('⏹️ Automation stopped', 'error');
    }

    updateStatus(status) {
        document.querySelector('.status-text').textContent = status;
    }

    updateExecutionStatus(status, progress) {
        document.getElementById('executionStatusText').textContent = status;
        if (progress !== null) {
            document.getElementById('progressFill').style.width = progress + '%';
        }
    }

    logExecution(message, type = 'info') {
        const logContainer = document.getElementById('executionLog');
        if (!logContainer) return;
        
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry log-${type}`;
        
        const timestamp = new Date().toLocaleTimeString();
        logEntry.innerHTML = `<span class="log-timestamp">[${timestamp}]</span> ${message}`;
        
        logContainer.appendChild(logEntry);
        logContainer.scrollTop = logContainer.scrollHeight;
    }

    showNotification(message, type = 'info') {
        console.log(`[${type.toUpperCase()}] ${message}`);
        
        const statusIndicator = document.getElementById('statusIndicator');
        if (statusIndicator) {
            const statusText = statusIndicator.querySelector('.status-text');
            if (statusText) {
                const originalText = statusText.textContent;
                statusText.textContent = message;
                
                setTimeout(() => {
                    statusText.textContent = originalText;
                }, 3000);
            }
        }
    }

    saveFlow() {
        const flowName = prompt('Masukkan nama flow:');
        if (!flowName) return;
        
        chrome.storage.local.get(['automationFlows'], (result) => {
            const flows = result.automationFlows || {};
            flows[flowName] = {
                events: this.flowEvents,
                created: new Date().toISOString(),
                description: prompt('Masukkan deskripsi flow (opsional):') || ''
            };
            chrome.storage.local.set({ automationFlows: flows });
            this.showNotification(`💾 Flow "${flowName}" disimpan`, 'success');
        });
    }

    loadFlow() {
        chrome.storage.local.get(['automationFlows'], (result) => {
            const flows = result.automationFlows || {};
            const flowNames = Object.keys(flows);
            
            if (flowNames.length === 0) {
                this.showNotification('📋 Tidak ada flow tersimpan', 'warning');
                return;
            }
            
            const flowName = prompt(`Flow tersedia: ${flowNames.join(', ')}\n\nMasukkan nama flow untuk dimuat:`);
            if (!flowName || !flows[flowName]) {
                this.showNotification('❌ Flow tidak ditemukan', 'error');
                return;
            }
            
            this.flowEvents = flows[flowName].events || [];
            this.renderFlowEvents();
            this.saveFlowEvents();
            this.showNotification(`📂 Flow "${flowName}" dimuat`, 'success');
        });
    }

    async testSingleEvent(index) {
        try {
            console.log(`Testing single event at index: ${index}`);
            
            if (!this.flowEvents || index < 0 || index >= this.flowEvents.length) {
                console.error('Invalid index or no flow events:', index, this.flowEvents);
                this.showNotification('❌ Invalid event index', 'error');
                return;
            }

            const event = this.flowEvents[index];
            const statusElement = document.getElementById(`eventStatus${index}`);

            // Update status to testing
            this.updateEventStatus(index, 'testing', 'Testing event...');

            this.showNotification(`🧪 Testing event ${index + 1}: ${event.type}`, 'info');
            this.logExecution(`Testing individual event: ${event.type} #${index + 1}`, 'info');

            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab) {
                throw new Error('No active tab found');
            }

            const response = await chrome.tabs.sendMessage(tab.id, {
                action: 'testEvent',
                event: event,
                index: index
            });

            if (response && response.success) {
                const result = response.result;

                if (result.success) {
                    this.updateEventStatus(index, 'success', `✅ Test passed: ${result.notes || 'Success'}`);
                    this.showNotification(`✅ Event ${index + 1} test passed`, 'success');
                    this.logExecution(`Event test passed: ${result.eventName || event.name || event.type}`, 'success');
                } else {
                    this.updateEventStatus(index, 'error', `❌ Test failed: ${result.error || result.notes || 'Unknown error'}`);
                    this.showNotification(`❌ Event ${index + 1} test failed: ${result.error || result.notes || 'Unknown error'}`, 'error');
                    this.logExecution(`Event test failed: ${result.eventName || event.name || event.type} - ${result.error || result.notes || 'Unknown error'}`, 'error');
                }
            } else {
                throw new Error(response?.error || 'Test execution failed');
            }

        } catch (error) {
            console.error('Test single event error:', error);
            this.updateEventStatus(index, 'error', `❌ Test error: ${error.message}`);
            this.showNotification(`❌ Event ${index + 1} test error: ${error.message}`, 'error');
            this.logExecution(`Event test error: ${error.message}`, 'error');
        }
    }

    updateEventStatus(index, status, message) {
        const statusElement = document.getElementById(`eventStatus${index}`);
        if (statusElement) {
            const indicator = statusElement.querySelector('.status-indicator');
            const text = statusElement.querySelector('.status-text');

            if (indicator) {
                indicator.setAttribute('data-status', status);

                // Update indicator color based on status
                switch (status) {
                    case 'success':
                        indicator.style.color = '#28a745';
                        break;
                    case 'error':
                        indicator.style.color = '#dc3545';
                        break;
                    case 'testing':
                        indicator.style.color = '#ffc107';
                        break;
                    default:
                        indicator.style.color = '#6c757d';
                }
            }

            if (text) {
                text.textContent = message;
            }
        }
    }

    async testAllEvents() {
        try {
            if (!this.flowEvents || this.flowEvents.length === 0) {
                this.showNotification('⚠️ No events to test', 'warning');
                return;
            }

            this.showNotification('🧪 Testing all events...', 'info');
            this.logExecution('Starting test of all events', 'info');

            let passedTests = 0;
            let failedTests = 0;

            for (let i = 0; i < this.flowEvents.length; i++) {
                try {
                    await this.testSingleEvent(i);

                    // Check if test passed by looking at the status
                    const statusElement = document.getElementById(`eventStatus${i}`);
                    const indicator = statusElement?.querySelector('.status-indicator');
                    const status = indicator?.getAttribute('data-status');

                    if (status === 'success') {
                        passedTests++;
                    } else {
                        failedTests++;
                    }

                    // Add delay between tests
                    await new Promise(resolve => setTimeout(resolve, 500));

                } catch (error) {
                    failedTests++;
                    console.error(`Test failed for event ${i}:`, error);
                }
            }

            const totalTests = this.flowEvents.length;
            const message = `🧪 Test completed: ${passedTests}/${totalTests} passed, ${failedTests} failed`;

            this.showNotification(message, passedTests === totalTests ? 'success' : 'warning');
            this.logExecution(`All events test completed: ${passedTests}/${totalTests} passed`, 'info');

        } catch (error) {
            this.showNotification(`❌ Test all events failed: ${error.message}`, 'error');
            this.logExecution(`Test all events error: ${error.message}`, 'error');
        }
    }

    deleteFlowEvent(index) {
        try {
            console.log(`Attempting to delete flow event at index: ${index}`);
            
            if (!this.flowEvents || index < 0 || index >= this.flowEvents.length) {
                console.error('Invalid index or no flow events:', index, this.flowEvents);
                this.showNotification('❌ Invalid event index', 'error');
                return;
            }

            const eventToDelete = this.flowEvents[index];
            const eventName = eventToDelete.name || eventToDelete.type || `Event ${index + 1}`;
            
            if (confirm(`Apakah Anda yakin ingin menghapus event "${eventName}"?`)) {
                this.flowEvents.splice(index, 1);
                this.renderFlowEvents();
                this.saveFlowEvents();
                this.showNotification(`🗑️ Event "${eventName}" telah dihapus`, 'success');
                this.logExecution(`Event deleted: ${eventName}`, 'info');
            }
        } catch (error) {
            console.error('Error deleting flow event:', error);
            this.showNotification(`❌ Error deleting event: ${error.message}`, 'error');
        }
    }
}

// Global reference for event handlers
let automationBot;

// Initialize Venus-Millware AutoFill when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    try {
        automationBot = new AutomationBotPopup();
        window.automationBot = automationBot;
        
        // Ensure global reference is available for onclick handlers
        if (typeof window !== 'undefined') {
            window.automationBot = automationBot;
        }
        
        console.log('AutomationBotPopup initialized successfully');
    } catch (error) {
        console.error('Failed to initialize AutomationBotPopup:', error);
    }
});
