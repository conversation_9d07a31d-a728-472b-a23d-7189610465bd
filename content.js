// Venus-Millware AutoFill - Content Script
// Alat otomatisasi untuk pengisian form dan input data otomatis
// Developer: Atha Rizki Pangestu - IT Rebinmas (Delloyd Group)

class AutomationBotContent {
    constructor() {
        this.isExecuting = false;
        this.isPaused = false;
        this.currentExecution = null;
        this.automationData = null;
        this.flowEvents = [];
        this.extractedData = {};
        this.executionResults = {
            success: false,
            eventsExecuted: 0,
            errors: [],
            extractedData: {},
            startTime: null,
            endTime: null
        };
        
        this.init();
    }

    init() {
        console.log('Venus-Millware AutoFill Content Script initialized');
        this.setupMessageListener();
        this.injectAdvancedHelpers();
    }

    setupMessageListener() {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // Keep the message channel open for async response
        });
    }

    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.action) {
                case 'executeAutomationFlow':
                    await this.executeAutomationFlow(message, sendResponse);
                    break;

                case 'pauseAutomation':
                    this.pauseAutomation();
                    sendResponse({ success: true });
                    break;

                case 'stopAutomation':
                    this.stopAutomation();
                    sendResponse({ success: true });
                    break;

                case 'testEvent':
                    await this.testSingleEvent(message.event, message.index, sendResponse);
                    break;

                case 'executeSingleEvent':
                    await this.executeSingleEvent(message.event, message.index, sendResponse);
                    break;

                case 'RUN_AUTOMATION_SEQUENCE':
                    await this.runAutomationSequence(message, sendResponse);
                    break;

                case 'extractData':
                    await this.extractPageData(message.selectors, sendResponse);
                    break;

                case 'getPageInfo':
                    sendResponse(this.getPageInfo());
                    break;

                case 'executePostLoginSequence':
                    await this.executePostLoginSequenceMessage(message, sendResponse);
                    break;

                case 'executeTextSearch':
                    await this.executeTextSearchMessage(message, sendResponse);
                    break;

                case 'executeTextSearchNavigate':
                    await this.executeTextSearchNavigateMessage(message, sendResponse);
                    break;

                case 'showTextSearchUI':
                    await this.showTextSearchUIMessage(message, sendResponse);
                    break;

                case 'testEvent':
                    await this.testEventMessage(message, sendResponse);
                    break;

                case 'preflightCheck':
                    await this.preflightCheckMessage(message, sendResponse);
                    break;

                case 'validateFlow':
                    await this.validateFlowMessage(message, sendResponse);
                    break;

                // Enhanced Element Targeting System
                case 'validateSelector':
                    await this.validateSelector(message, sendResponse);
                    break;

                case 'highlightElement':
                    await this.highlightElement(message, sendResponse);
                    break;

                case 'getElementInfo':
                    await this.getElementInfo(message, sendResponse);
                    break;

                case 'generateSelectorAlternatives':
                    await this.generateSelectorAlternatives(message, sendResponse);
                    break;

                case 'findElementsByText':
                    await this.findElementsByText(message, sendResponse);
                    break;

                case 'showElementManager':
                    this.showElementManagerInterface();
                    sendResponse({ success: true });
                    break;

                case 'popup_handler':
                    await this.executePopupHandlerEvent(message);
                    sendResponse({ success: true, popupHandled: true });
                    break;

                case 'wait_for_page_stability':
                    await this.executeWaitForPageStabilityEvent(message);
                    sendResponse({ success: true });
                    break;

                case 'detect_and_click_popup_ok':
                    await this.executeDetectAndClickPopupOkEvent(message);
                    break;

                case 'executeEvent':
                    await this.executeEvent(message.event, message.index || 0);
                    sendResponse({ success: true });
                    break;

                case 'prevent_redirect':
                    await this.executePreventRedirectEvent(event);
                    break;

                default:
                    console.warn('Unknown automation action:', message.action);
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Error handling automation message:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async executeAutomationFlow(message, sendResponse) {
        if (this.isExecuting) {
            sendResponse({ success: false, error: 'Automation already running' });
            return;
        }

        console.log('🚀 Starting automation flow execution');
        
        this.isExecuting = true;
        this.isPaused = false;
        this.flowEvents = message.flowEvents || [];
        this.automationData = message.automationData || [];
        this.executionResults = {
            success: false,
            eventsExecuted: 0,
            errors: [],
            extractedData: {},
            startTime: new Date().toISOString(),
            endTime: null,
            executionId: message.metadata?.executionId || this.generateExecutionId()
        };

        try {
            // Send initial response
            sendResponse({ success: true, message: 'Automation flow started' });

            // Execute the flow
            await this.runFlowSequence();

            // Mark as successful if we got here
            this.executionResults.success = true;
            this.executionResults.endTime = new Date().toISOString();

            this.notifyCompletion(true, null, this.executionResults);

        } catch (error) {
            console.error('❌ Automation flow execution failed:', error);
            this.executionResults.success = false;
            this.executionResults.endTime = new Date().toISOString();
            this.executionResults.errors.push({
                message: error.message,
                timestamp: new Date().toISOString(),
                stack: error.stack
            });
            
            this.notifyCompletion(false, error.message, this.executionResults);
        } finally {
            this.isExecuting = false;
        }
    }

    async runFlowSequence() {
        console.log(`📋 Executing ${this.flowEvents.length} automation events`);
        console.log(`📍 Starting URL: ${window.location.href}`);
        console.log(`🔍 Flow events:`, this.flowEvents);
        
        for (let i = 0; i < this.flowEvents.length; i++) {
            if (!this.isExecuting || this.isPaused) {
                console.log('⏸️ Automation paused or stopped');
                break;
            }

            const event = this.flowEvents[i];
            console.log(`\n🎯 ========== EXECUTING EVENT ${i + 1}/${this.flowEvents.length} ==========`);
            console.log(`📝 Event Type: ${event.type}`);
            console.log(`📄 Description: ${event.description || 'No description'}`);
            console.log(`📍 Current URL: ${window.location.href}`);
            console.log(`🔍 Page State: ${document.readyState}`);
            console.log(`📊 Event Details:`, event);

            try {
                const eventStartTime = Date.now();
                await this.executeEvent(event, i);
                const eventDuration = Date.now() - eventStartTime;
                
                this.executionResults.eventsExecuted++;
                console.log(`✅ Event ${i + 1} completed successfully in ${eventDuration}ms`);

                // Update progress
                const progress = Math.round(((i + 1) / this.flowEvents.length) * 100);
                this.notifyProgress(progress, `Completed event ${i + 1}: ${event.type}`);
                console.log(`📊 Progress: ${progress}% (${i + 1}/${this.flowEvents.length})`);

                // Add delay between events
                const interEventDelay = 100 + Math.random() * 100;
                console.log(`⏳ Inter-event delay: ${interEventDelay}ms`);
                await this.delay(interEventDelay);

                // Log post-event state
                console.log(`📍 Post-event URL: ${window.location.href}`);
                console.log(`🔍 Post-event page state: ${document.readyState}`);

            } catch (error) {
                console.error(`❌ ========== EVENT ${i + 1} FAILED ==========`);
                console.error(`📝 Event Type: ${event.type}`);
                console.error(`📄 Description: ${event.description || 'No description'}`);
                console.error(`❌ Error Message: ${error.message}`);
                console.error(`📍 URL when error occurred: ${window.location.href}`);
                console.error(`🔍 Page state when error occurred: ${document.readyState}`);
                console.error(`📊 Full error:`, error);
                
                this.executionResults.errors.push({
                    eventIndex: i,
                    eventType: event.type,
                    eventDescription: event.description,
                    message: error.message,
                    timestamp: new Date().toISOString(),
                    url: window.location.href,
                    pageState: document.readyState,
                    stack: error.stack
                });
                
                // Decide whether to continue or stop based on error type
                if (this.isCriticalError(error)) {
                    console.error(`🚨 CRITICAL ERROR - Stopping automation`);
                    throw error;
                }
                
                // Continue with next event for non-critical errors
                console.log('⚠️ Non-critical error, continuing with next event');
                console.log(`📊 Continuing to event ${i + 2}/${this.flowEvents.length}`);
            }
        }
        
        console.log(`🏁 Flow sequence completed. Events executed: ${this.executionResults.eventsExecuted}/${this.flowEvents.length}`);
        console.log(`📍 Final URL: ${window.location.href}`);
        console.log(`📊 Final results:`, this.executionResults);
    }

    async executeEvent(event, index) {
        console.log(`🔧 Executing ${event.type} event:`, event);

        // Evaluate conditional logic if present
        if (event.condition) {
            const conditionResult = await this.evaluateEventCondition(event.condition);
            if (!conditionResult) {
                console.log(`⏭️ Skipping event ${index + 1} - condition not met: ${JSON.stringify(event.condition)}`);
                return;
            }
        }

        // Execute the event based on type
        switch (event.type) {
            case 'click':
                await this.executeClickEvent(event);
                break;
                
            case 'input':
                await this.executeInputEvent(event);
                break;
                
            case 'wait':
                await this.executeWaitEvent(event);
                break;
                
            case 'extract':
                await this.executeExtractEvent(event);
                break;
                
            case 'navigate':
            case 'open_to':
                await this.executeNavigateEvent(event);
                break;
                
            case 'scroll':
                await this.executeScrollEvent(event);
                break;
                
            case 'conditional_action':
                await this.executeConditionEvent(event, index);
                break;

            case 'popup_handler':
                await this.executePopupHandlerEvent(event);
                break;

            case 'wait_for_page_stability':
                await this.executeWaitForPageStabilityEvent(event);
                break;

            case 'detect_and_click_popup_ok':
                await this.executeDetectAndClickPopupOkEvent(event);
                break;

            case 'prevent_redirect':
                await this.executePreventRedirectEvent(event);
                break;

            default:
                console.warn(`⚠️ Unknown event type: ${event.type}`);
                break;
        }

        // Notify about interaction
        this.notifyInteraction(event.type, event.selector || event.url || 'N/A', true);
    }

    async executeClickEvent(event) {
        console.log(`🖱️ Executing click event on: ${event.selector || event.searchText || 'text search'}`);
        console.log(`🔍 Click event details:`, event);

        let element;
        
        // Enhanced popup context handling
        if (event.popupContext) {
            console.log('🔍 Looking for element within popup context');
            const popupElement = document.querySelector(event.popupContext);
            if (popupElement) {
                element = popupElement.querySelector(event.selector);
                console.log(`${element ? '✅' : '❌'} Element ${element ? 'found' : 'not found'} in popup context`);
            }
        }

        // If not found in popup context, use standard methods
        if (!element) {
            if (event.searchText || event.searchTexts) {
                const searchTexts = event.searchTexts || [event.searchText];
                console.log(`🔍 Searching for element by text: ${searchTexts.join(', ')}`);
                element = await this.findElementByText(searchTexts, event.timeout || 3000);
            } else if (event.selector) {
                console.log(`🔍 Searching for element by selector: ${event.selector} (type: ${event.selectorType || 'css'})`);
                
                // Special handling for Human Resource link
                if (event.selector.includes('popout') && event.selector.includes('level1')) {
                    console.log('🎯 Special handling for Human Resource link');
                    
                    // Try multiple selector variations for Human Resource
                    const hrSelectors = [
                        'a.popout.level1.static',
                        'a[class*="popout"][class*="level1"]',
                        'a[class*="popout level1"]',
                        'a.popout',
                        'a:contains("Human Resource")',
                        'a[title*="Human"]',
                        'a[href*="HR"]'
                    ];
                    
                    for (const selector of hrSelectors) {
                        console.log(`🔍 Trying selector: ${selector}`);
                        try {
                            if (selector.includes(':contains')) {
                                // Use text-based search for :contains
                                element = await this.findElementByText(['Human Resource', 'HR'], 2000);
                            } else {
                                element = await this.findElement(selector, 'css', 2000);
                            }
                            
                            if (element) {
                                console.log(`✅ Found Human Resource element with selector: ${selector}`);
                                break;
                            }
                        } catch (err) {
                            console.log(`❌ Selector ${selector} failed: ${err.message}`);
                        }
                    }
                    
                    // If still not found, try comprehensive text search
                    if (!element) {
                        console.log('🔍 Trying comprehensive text search for Human Resource');
                        const allLinks = document.querySelectorAll('a');
                        for (const link of allLinks) {
                            const text = link.textContent.trim().toLowerCase();
                            const classes = link.className.toLowerCase();
                            if (text.includes('human') || text.includes('hr') || 
                                classes.includes('popout') || classes.includes('level1')) {
                                console.log(`🎯 Found potential HR link: "${link.textContent.trim()}" with classes: ${link.className}`);
                                element = link;
                                break;
                            }
                        }
                    }
                } else {
                    // Standard element finding
                    element = await this.findElementWithMultipleMethods(
                        event.selector, 
                        event.selectorType || 'css',
                        event.alternatives || []
                    );
                }
            } else {
                throw new Error('Click event requires either selector or searchText');
            }
        }

        if (!element) {
            // Enhanced error message with debugging info
            console.error('❌ Click target not found. Available elements:');
            if (event.selector && event.selector.includes('popout')) {
                // Log all links for debugging
                const allLinks = document.querySelectorAll('a');
                allLinks.forEach((link, index) => {
                    console.log(`Link ${index}: "${link.textContent.trim()}" - classes: "${link.className}" - href: "${link.href}"`);
                });
            }
            throw new Error(`Click target not found: ${event.selector || event.searchText}`);
        }

        console.log(`✅ Found element: ${element.tagName} with text: "${element.textContent.trim()}" and classes: "${element.className}"`);

        // Enhanced clickability check with popup context
        await this.ensureElementClickable(element);

        // Scroll to element if needed
        this.scrollElementIntoView(element);
        await this.delay(100);

        // Enhanced clicking with popup context support
        console.log('🖱️ Clicking element...');
        
        // Focus first for better reliability
        try {
            element.focus();
            await this.delay(50);
        } catch (focusError) {
            console.warn('⚠️ Could not focus element:', focusError.message);
        }

        // Multiple click approaches for maximum reliability
        const clickMethods = [
            () => element.click(),
            () => {
                const event = new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true,
                    view: window
                });
                element.dispatchEvent(event);
            },
            () => {
                // Simulate full mouse interaction
                element.dispatchEvent(new MouseEvent('mousedown', { bubbles: true }));
                element.dispatchEvent(new MouseEvent('mouseup', { bubbles: true }));
                element.dispatchEvent(new MouseEvent('click', { bubbles: true }));
            }
        ];

        // Try each click method
        for (let i = 0; i < clickMethods.length; i++) {
            try {
                clickMethods[i]();
                console.log(`✅ Click method ${i + 1} executed successfully`);
                break;
            } catch (clickError) {
                console.warn(`⚠️ Click method ${i + 1} failed:`, clickError.message);
                if (i === clickMethods.length - 1) {
                    throw new Error(`All click methods failed: ${clickError.message}`);
                }
            }
        }

        // Wait after click if specified
        if (event.waitAfterClick || event.wait_after_click) {
            const waitTime = event.waitAfterClick || event.wait_after_click;
            console.log(`⏳ Waiting ${waitTime}ms after click`);
            await this.delay(waitTime);
        }

        console.log(`✅ Click event completed successfully on element: ${element.tagName}.${element.className}`);

        // Notify successful interaction
        this.notifyInteraction('click', event.selector || event.searchText, true);
    }

    async findElementWithMultipleMethods(primarySelector, selectorType = 'css', alternatives = []) {
        console.log(`🔍 Searching for element with multiple methods: ${primarySelector}`);
        
        // Method 1: Primary selector
        let element = await this.findElement(primarySelector, selectorType, 2000);
        if (element) {
            console.log(`✅ Found with primary selector: ${primarySelector}`);
            return element;
        }

        // Method 2: Alternative selectors
        if (alternatives && alternatives.length > 0) {
            for (const altSelector of alternatives) {
                console.log(`🔍 Trying alternative selector: ${altSelector}`);
                element = await this.findElement(altSelector, 'css', 1000);
                if (element) {
                    console.log(`✅ Found with alternative selector: ${altSelector}`);
                    return element;
                }
            }
        }

        // Method 3: Text-based search for login buttons
        if (primarySelector.includes('btn') || primarySelector.includes('Login')) {
            console.log(`🔍 Trying text-based search for login button...`);
            element = await this.findElementByText(['LOG IN', 'Login', 'Sign In', 'MASUK', 'log in']);
            if (element) {
                console.log(`✅ Found with text search`);
                return element;
            }
        }

        // Method 4: Advanced search for common login button patterns
        const loginButtonSelectors = [
            // Standard patterns
                'input[type="submit"]',
            'button[type="submit"]',
            'input[value*="LOG"]',
            'input[value*="Login"]',
            'input[value*="MASUK"]',
            'button[value*="LOG"]',
            'button[value*="Login"]',
            
            // ID and name patterns
            '#btnLogin', '#loginBtn', '#login', '#submit',
            'input[name*="login"]', 'input[name*="Login"]',
            'input[name*="btn"]', 'button[name*="login"]',
            
            // Class patterns  
            '.login-btn', '.btn-login', '.submit-btn',
            '.button[class*="login"]', '.btn[class*="submit"]',
            
            // Generic button patterns
            'button:not([type="button"]):not([type="reset"])',
            'input[type="submit"]:not([style*="display: none"])',
            
            // Specific value patterns
            'input[value="LOG IN"]', 'input[value="Login"]',
            'input[value="MASUK"]', 'input[value="Submit"]',
            'button[value="LOG IN"]', 'button[value="Login"]'
        ];

        console.log(`🔍 Trying comprehensive login button search...`);
        for (const selector of loginButtonSelectors) {
            element = await this.findElement(selector, 'css', 500);
            if (element && this.isElementVisible(element)) {
                console.log(`✅ Found with comprehensive search: ${selector}`);
                return element;
            }
        }

        // Method 5: Search by form submission context
        console.log(`🔍 Trying form context search...`);
        const forms = document.querySelectorAll('form');
        for (const form of forms) {
            const submitButtons = form.querySelectorAll('input[type="submit"], button[type="submit"], button:not([type])');
            for (const btn of submitButtons) {
                if (this.isElementVisible(btn)) {
                    console.log(`✅ Found submit button in form context`);
                    return btn;
                }
            }
        }

        console.log(`❌ Element not found with any method`);
        return null;
    }

    async findElementByText(textArray, timeout = 3000) {
        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            try {
                // Search for buttons/inputs with matching text or value
                const allElements = document.querySelectorAll('button, input[type="submit"], input[type="button"], a');
                
                for (const element of allElements) {
                    if (!this.isElementVisible(element)) continue;
                    
                    const text = element.textContent || element.value || element.innerText || '';
                    const trimmedText = text.trim().toUpperCase();
                    
                    for (const searchText of textArray) {
                        if (trimmedText.includes(searchText.toUpperCase())) {
                            console.log(`Found element by text: "${searchText}" -> "${text}"`);
                            return element;
                        }
                    }
                }
                
                await this.delay(200);
            } catch (error) {
                console.warn('Error in text-based search:', error);
                await this.delay(200);
            }
        }
        
        return null;
    }

    // Advanced text search method - like Ctrl+F
    async findElementByTextAdvanced(searchText, options = {}) {
        const {
            timeout = 3000,
            caseSensitive = false,
            exactMatch = false,
            elementTypes = ['*'], // Can specify specific elements
            excludeHidden = true
        } = options;

        console.log(`🔍 Advanced text search for: "${searchText}"`);
        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            try {
                // Get all elements based on type specification
                const selector = elementTypes.includes('*') ? '*' : elementTypes.join(', ');
                const allElements = document.querySelectorAll(selector);
                
                for (const element of allElements) {
                    // Skip hidden elements if specified
                    if (excludeHidden && !this.isElementVisible(element)) continue;
                    
                    // Get all possible text content
                    const texts = [
                        element.textContent,
                        element.innerText,
                        element.value,
                        element.title,
                        element.getAttribute('alt'),
                        element.getAttribute('placeholder')
                    ].filter(Boolean);
                    
                    for (const text of texts) {
                        let processedText = text.trim();
                        let processedSearch = searchText.trim();
                        
                        if (!caseSensitive) {
                            processedText = processedText.toUpperCase();
                            processedSearch = processedSearch.toUpperCase();
                        }
                        
                        const found = exactMatch ? 
                            processedText === processedSearch : 
                            processedText.includes(processedSearch);
                            
                        if (found) {
                            console.log(`✅ Advanced search found: "${searchText}" in ${element.tagName} with text "${text}"`);
                            return element;
                        }
                    }
                }
                
                await this.delay(100);
            } catch (error) {
                console.warn('Error in advanced text search:', error);
                await this.delay(100);
            }
        }
        
        console.log(`❌ Advanced search timeout: "${searchText}"`);
        return null;
    }

    // Quick text search - optimized for speed
    async quickTextSearch(searchText, timeout = 1000) {
        console.log(`⚡ Quick text search for: "${searchText}"`);
        const startTime = Date.now();
        
        // Quick search in common interactive elements
        const commonSelectors = [
            'button',
            'input[type="submit"]',
            'input[type="button"]',
            'a',
            '[onclick]',
            '.button',
            '.btn'
        ];
        
        while (Date.now() - startTime < timeout) {
            for (const selector of commonSelectors) {
                const elements = document.querySelectorAll(selector);
                
                for (const element of elements) {
                    if (!this.isElementVisible(element)) continue;
                    
                    const text = (element.textContent || element.value || '').trim().toUpperCase();
                    if (text.includes(searchText.toUpperCase())) {
                        console.log(`⚡ Quick search found: "${searchText}" in ${element.tagName}`);
                        return element;
                    }
                }
            }
            await this.delay(50);
        }
        
        return null;
    }

    async executeInputEvent(event) {
        let element = await this.findElement(event.selector, event.selectorType);

        if (!element) {
            // Try alternative selectors based on the field type
            const isPasswordField = event.value === 'adm075' && event.selector.includes('password');
            const isUsernameField = event.value === 'adm075' && !event.selector.includes('password');

            let alternativeSelectors = [];

            if (isUsernameField) {
                alternativeSelectors = [
                    'input[type="text"]',
                    'input[name*="user"]',
                    'input[id*="user"]',
                    'input[placeholder*="user"]',
                    'input[name*="login"]',
                    'input[id*="login"]',
                    'input:not([type="password"]):not([type="hidden"]):not([type="submit"])'
                ];
            } else if (isPasswordField) {
                alternativeSelectors = [
                    'input[type="password"]',
                    'input[name*="pass"]',
                    'input[id*="pass"]',
                    'input[name*="pwd"]',
                    'input[id*="pwd"]'
                ];
            }

            for (const altSelector of alternativeSelectors) {
                element = await this.findElement(altSelector, 'css');
                if (element) {
                    console.log(`Found alternative element with selector: ${altSelector}`);
                    break;
                }
            }

            if (!element) {
                throw new Error(`Input target not found: ${event.selector}`);
            }
        }

        // Determine the value to input
        let inputValue = event.value;

        if (event.dataMapping && this.automationData) {
            inputValue = this.getDataValue(event.dataMapping) || event.value;
        }

        if (!inputValue) {
            throw new Error('No value specified for input event');
        }

        // Scroll to element and focus
        this.scrollElementIntoView(element);
        await this.delay(100);

        // Focus the element first
        element.focus();
        await this.delay(100);

        // Clear field if requested
        if (event.clearFirst !== false) {
            element.value = '';
            element.dispatchEvent(new Event('input', { bubbles: true }));
            await this.delay(100);
        }

        // Use advanced typing simulation
        if (window.venusAutoFill && window.venusAutoFill.simulateHumanType) {
            await window.venusAutoFill.simulateHumanType(element, inputValue, {
                clearFirst: event.clearFirst !== false
            });
        } else {
            // Fallback to simple input with better event simulation
            element.value = inputValue;

            // Dispatch multiple events for better compatibility
            element.dispatchEvent(new Event('input', { bubbles: true }));
            element.dispatchEvent(new Event('change', { bubbles: true }));
            element.dispatchEvent(new Event('keyup', { bubbles: true }));
        }

        console.log(`✅ Input value "${inputValue}" into: ${event.selector}`);

        // Wait after input if specified
        if (event.wait_after_input) {
            console.log(`⏳ Waiting ${event.wait_after_input}ms after input`);
            await this.delay(event.wait_after_input);
        }
    }

    async executeWaitEvent(event) {
        const duration = event.duration || 1000;
        
        if (event.waitFor === 'element' && event.condition) {
            // Wait for element to appear
            console.log(`⏳ Waiting for element: ${event.condition}`);
            await this.waitForElement(event.condition, duration);
        } else if (event.waitFor === 'navigation') {
            // Wait for navigation to complete
            console.log(`⏳ Waiting for navigation (max ${duration}ms)`);
            await this.waitForNavigation(duration);
        } else {
            // Simple time delay
            console.log(`⏳ Waiting ${duration}ms`);
            await this.delay(duration);
        }

        console.log(`✅ Wait completed`);
    }

    async executeExtractEvent(event) {
        const element = await this.findElement(event.selector, event.selectorType);
        
        if (!element) {
            throw new Error(`Extract target not found: ${event.selector}`);
        }

        let extractedValue;
        
        switch (event.attribute) {
            case 'text':
                extractedValue = element.textContent?.trim();
                break;
            case 'value':
                extractedValue = element.value;
                break;
            case 'href':
                extractedValue = element.href;
                break;
            case 'src':
                extractedValue = element.src;
                break;
            case 'innerHTML':
                extractedValue = element.innerHTML;
                break;
            default:
                extractedValue = element.getAttribute(event.attribute);
        }

        // Store extracted data
        if (event.variableName) {
            this.extractedData[event.variableName] = extractedValue;
            this.executionResults.extractedData[event.variableName] = extractedValue;
        }

        console.log(`✅ Extracted ${event.attribute} from ${event.selector}: "${extractedValue}"`);
        
        // Notify about data extraction
        this.notifyDataExtraction(1);
    }

    async executeNavigateEvent(event) {
        const url = event.url || event.selector;
        
        if (!url) {
            throw new Error('Navigate event requires URL');
        }

        console.log(`🌐 Navigating to: ${url}`);
        console.log(`📍 Current URL: ${window.location.href}`);
        console.log(`🔍 Event details:`, event);

        // Check if we need to wait for popup dismissal first
        if (event.waitForPopupDismissal) {
            console.log('⏳ Waiting for any popup dismissal before navigation...');
            await this.delay(event.popupDismissalDelay || 1000);
        }

        // Check if page is ready for navigation
        if (event.ensurePageReady !== false) {
            await this.waitForPageStability();
        }

        try {
            // Enhanced navigation with error handling
            if (event.newTab) {
                // Open in new tab
                console.log('⚠️ Opening in new tab - automation may not continue in new tab');
                window.open(url, '_blank');
                console.log(`✅ Opened URL in new tab: ${url}`);
                
                // Add warning about tab context
                this.notifyInteraction('navigate_new_tab', url, true);
                console.warn('🚨 WARNING: Automation will NOT continue in the new tab. Use "navigate" type instead for same-tab navigation.');
            } else {
                // Navigate current tab - RECOMMENDED for automation flows
                console.log('🔄 Navigating in current tab - automation will continue');
                
                // Store current state before navigation
                const currentState = {
                    isExecuting: this.isExecuting,
                    currentEventIndex: this.currentEventIndex,
                    flowEvents: this.flowEvents
                };
                
                window.location.href = url;
                console.log(`✅ Navigation initiated to: ${url}`);
                
                // Wait for navigation to complete if specified
                if (event.waitForLoad !== false) {
                    const navigationTimeout = event.timeout || 30000;
                    console.log(`⏳ Waiting for navigation to complete (timeout: ${navigationTimeout}ms)`);
                    await this.waitForNavigation(navigationTimeout);
                    
                    // Log post-navigation state
                    console.log(`📍 Navigation completed. New URL: ${window.location.href}`);
                    console.log(`🔍 Page readyState: ${document.readyState}`);
                }
            }

            // Additional stability wait after navigation
            if (event.stabilityDelay) {
                console.log(`⏳ Additional stability wait: ${event.stabilityDelay}ms`);
                await this.delay(event.stabilityDelay);
            }

            // Verify we're on the expected page
            if (window.location.href.includes(url) || url.includes(window.location.pathname)) {
                console.log('✅ Navigation verification successful');
            } else {
                console.warn(`⚠️ Navigation verification failed. Expected: ${url}, Actual: ${window.location.href}`);
            }

        } catch (error) {
            console.error('❌ Navigation failed:', error);
            console.error('📍 Current URL when error occurred:', window.location.href);
            throw new Error(`Navigation failed to ${url}: ${error.message}`);
        }
    }

    /**
     * Wait for page stability (enhanced version)
     */
    async waitForPageStability(timeout = 5000) {
        console.log('⏳ Ensuring page stability...');
        
        const startTime = Date.now();
        let stableStart = null;
        const requiredStabilityDuration = 1000; // 1 second of stability
        
        while (Date.now() - startTime < timeout) {
            const isStable = document.readyState === 'complete' && 
                           !document.querySelector('.PopupBoxLogin') && // No popup visible
                           !document.querySelector('[class*="loading"]'); // No loading indicators
            
            if (isStable) {
                if (!stableStart) {
                    stableStart = Date.now();
                }
                
                if (Date.now() - stableStart >= requiredStabilityDuration) {
                    console.log('✅ Page stability confirmed');
                    return true;
                }
            } else {
                stableStart = null; // Reset stability timer
            }
            
            await this.delay(100);
        }
        
        console.warn('⚠️ Page stability timeout reached');
        return false;
    }

    async executeScrollEvent(event) {
        const distance = event.distance || 500;
        const direction = event.direction || 'down';
        
        let scrollOptions = { behavior: 'smooth' };
        
        if (event.target) {
            // Scroll to specific element
            const targetElement = await this.findElement(event.target);
            if (targetElement) {
                targetElement.scrollIntoView(scrollOptions);
            }
        } else {
            // Scroll page
            let scrollX = 0, scrollY = 0;
            
            switch (direction) {
                case 'down':
                    scrollY = distance;
                    break;
                case 'up':
                    scrollY = -distance;
                    break;
                case 'right':
                    scrollX = distance;
                    break;
                case 'left':
                    scrollX = -distance;
                    break;
            }
            
            window.scrollBy({ left: scrollX, top: scrollY, ...scrollOptions });
        }

        await this.delay(1000); // Wait for scroll to complete
        console.log(`✅ Scrolled ${direction} ${distance}px`);
    }

    async executeConditionEvent(event, currentIndex) {
        // Basic condition evaluation
        const conditionResult = await this.evaluateCondition(event.condition);
        
        console.log(`🔍 Condition "${event.condition}" evaluated to: ${conditionResult}`);
        
        // This is a simplified implementation
        // In a full implementation, you'd handle jumping to different events
        if (conditionResult && event.trueAction) {
            console.log(`✅ Condition true, would execute: ${event.trueAction}`);
        } else if (!conditionResult && event.falseAction) {
            console.log(`❌ Condition false, would execute: ${event.falseAction}`);
        }
    }

    async findElement(selector, selectorType = 'css', timeout = 3000) {
        if (!selector) {
            throw new Error('Selector is required');
        }

        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            let element;
            
            try {
                switch (selectorType) {
                    case 'xpath':
                        const result = document.evaluate(
                            selector,
                            document,
                            null,
                            XPathResult.FIRST_ORDERED_NODE_TYPE,
                            null
                        );
                        element = result.singleNodeValue;
                        break;
                        
                    case 'text':
                        if (window.venusAutoFill && window.venusAutoFill.findElementByText) {
                            element = window.venusAutoFill.findElementByText(selector);
                        } else {
                            // Fallback text search
                            element = Array.from(document.querySelectorAll('*'))
                                .find(el => el.textContent?.includes(selector));
                        }
                        break;
                        
                    default: // css
                        element = document.querySelector(selector);
                }
                
                if (element && this.isElementVisible(element)) {
                    return element;
                }
            } catch (error) {
                console.warn(`Error finding element with ${selectorType} selector "${selector}":`, error);
            }
            
            await this.delay(50);
        }
        
        return null;
    }

    isElementVisible(element) {
        if (!element) return false;
        
        const rect = element.getBoundingClientRect();
        const style = window.getComputedStyle(element);
        
        return rect.width > 0 && 
               rect.height > 0 && 
               style.visibility !== 'hidden' && 
               style.display !== 'none' &&
               style.opacity !== '0';
    }

    scrollElementIntoView(element) {
        if (element && typeof element.scrollIntoView === 'function') {
            element.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
                inline: 'center'
            });
        }
    }

    getDataValue(path) {
        // Extract value from automation data using dot notation
        // e.g., "user.email" -> automationData.user.email
        if (!this.automationData || !Array.isArray(this.automationData) || this.automationData.length === 0) {
            return null;
        }

        const pathParts = path.split('.');
        let value = this.automationData[0]; // Use first record for simplicity
        
        for (const part of pathParts) {
            if (value && typeof value === 'object' && part in value) {
                value = value[part];
            } else {
                return null;
            }
        }
        
        return value;
    }

    async waitForElement(selector, timeout = 10000) {
        const element = await this.findElement(selector, 'css', timeout);
        if (!element) {
            throw new Error(`Element not found within ${timeout}ms: ${selector}`);
        }
        return element;
    }

    async waitForNavigation(timeout = 30000) {
        return new Promise((resolve) => {
            const startTime = Date.now();
            
            const checkReady = () => {
                if (document.readyState === 'complete' || Date.now() - startTime > timeout) {
                    resolve();
                } else {
                    setTimeout(checkReady, 100);
                }
            };
            
            checkReady();
        });
    }

    async evaluateCondition(condition) {
        // Simple condition evaluation
        // In a full implementation, this would be much more sophisticated
        try {
            if (condition.includes('element.exists(')) {
                const selectorMatch = condition.match(/element\.exists\(['"`]([^'"`]+)['"`]\)/);
                if (selectorMatch) {
                    const selector = selectorMatch[1];
                    const element = document.querySelector(selector);
                    return !!element;
                }
            }
            
            // Add more condition types as needed
            return false;
        } catch (error) {
            console.error('Error evaluating condition:', error);
            return false;
        }
    }

    isCriticalError(error) {
        const criticalPatterns = [
            'Navigation failed',
            'Page not loaded',
            'Authentication required'
        ];
        
        return criticalPatterns.some(pattern => error.message.includes(pattern));
    }

    pauseAutomation() {
        this.isPaused = true;
        console.log('⏸️ Automation paused');
    }

    stopAutomation() {
        this.isExecuting = false;
        this.isPaused = false;
        console.log('⏹️ Automation stopped');
    }

    async testSingleEvent(event, index, sendResponse) {
        try {
            console.log(`🧪 Testing single event ${index}: ${event.name || event.type}`);
            
            // Convert JSON flow event to execution format if needed
            const testEvent = event.parameters ? this.convertJsonEventToExecution(event) : event;
            
            // Perform enhanced validation based on event type
            const validationResult = await this.validateEventForTesting(testEvent, index);
            
            if (!validationResult.isValid) {
                sendResponse({
                    success: false,
                    error: validationResult.error,
                    details: validationResult.details,
                    eventType: event.type,
                    eventName: event.name
                });
                return;
            }

            // Store original state
            const originalExecuting = this.isExecuting;
            
            // Set test mode
            this.isExecuting = true;
            
            // Create test result object
            const testResult = {
                eventIndex: index,
                eventType: event.type,
                eventName: event.name || `Event ${index + 1}`,
                startTime: Date.now(),
                success: false,
                error: null,
                details: {},
                elementInfo: null
            };

            try {
                // Test specific event type
                switch (testEvent.type) {
                    case 'click':
                        testResult.details = await this.testClickEventEnhanced(testEvent);
                        break;
                        
                    case 'input':
                        testResult.details = await this.testInputEventEnhanced(testEvent);
                        break;
                        
                    case 'navigate':
                        testResult.details = await this.testNavigateEventEnhanced(testEvent);
                        break;
                        
                    case 'wait':
                        testResult.details = await this.testWaitEventEnhanced(testEvent);
                        break;
                        
                    case 'text_search_click':
                        testResult.details = await this.testTextSearchClickEventEnhanced(testEvent);
                        break;
                        
                    case 'extract':
                        testResult.details = await this.testExtractEventEnhanced(testEvent);
                        break;
                        
                    default:
                        testResult.details = await this.testGenericEventEnhanced(testEvent);
                }
                
                testResult.success = true;
                testResult.endTime = Date.now();
                testResult.duration = testResult.endTime - testResult.startTime;
                
                console.log(`✅ Single event test passed: ${event.name}`);
                sendResponse({
                    success: true,
                    message: `Event test passed: ${event.name}`,
                    testResult: testResult
                });
                
            } catch (testError) {
                testResult.success = false;
                testResult.error = testError.message;
                testResult.endTime = Date.now();
                testResult.duration = testResult.endTime - testResult.startTime;
                
                console.error(`❌ Single event test failed:`, testError);
                sendResponse({
                    success: false,
                    error: testError.message,
                    testResult: testResult
                });
            }
            
        } catch (error) {
            console.error('❌ Event test setup failed:', error);
            sendResponse({
                success: false,
                error: error.message,
                eventType: event.type,
                eventName: event.name
            });
        } finally {
            // Restore original state
            this.isExecuting = originalExecuting;
        }
    }

    /**
     * Validate event for testing
     */
    async validateEventForTesting(event, index) {
        const validation = {
            isValid: true,
            error: null,
            details: {}
        };

        try {
            // Basic validation
            if (!event.type) {
                validation.isValid = false;
                validation.error = 'Event type is required';
                return validation;
            }

            // Type-specific validation
            switch (event.type) {
                case 'click':
                    if (!event.selector && !event.searchTexts) {
                        validation.isValid = false;
                        validation.error = 'Click event requires selector or searchTexts';
                    }
                    break;
                    
                case 'input':
                    if (!event.selector) {
                        validation.isValid = false;
                        validation.error = 'Input event requires selector';
                    }
                    if (event.value === undefined && !event.dataMapping) {
                        validation.isValid = false;
                        validation.error = 'Input event requires value or dataMapping';
                    }
                    break;
                    
                case 'navigate':
                    if (!event.url) {
                        validation.isValid = false;
                        validation.error = 'Navigate event requires URL';
                    }
                    break;
                    
                case 'wait':
                    if (!event.duration && !event.waitForElement) {
                        validation.isValid = false;
                        validation.error = 'Wait event requires duration or waitForElement';
                    }
                    break;
            }

            return validation;

        } catch (error) {
            validation.isValid = false;
            validation.error = `Validation failed: ${error.message}`;
            return validation;
        }
    }

    /**
     * Enhanced click event testing
     */
    async testClickEventEnhanced(event) {
        const testResult = {
            elementFound: false,
            elementInfo: null,
            targetingMethod: null,
            clickable: false,
            errorDetails: []
        };

        try {
            let element = null;
            
            // Try enhanced targeting if selector is available
            if (event.selector) {
                const targetConfig = {
                    selector: event.selector,
                    alternatives: event.selectorAlternatives
                };
                
                const result = await this.findElementWithEnhancedTargeting(targetConfig);
                if (result.success) {
                    element = result.element;
                    testResult.targetingMethod = result.method;
                    testResult.elementFound = true;
                }
            }
            
            // Try text search if no element found and searchTexts available
            if (!element && event.searchTexts) {
                try {
                    element = await this.findElementByText(event.searchTexts);
                    if (element) {
                        testResult.elementFound = true;
                        testResult.targetingMethod = 'text_search';
                    }
                } catch (error) {
                    testResult.errorDetails.push(`Text search failed: ${error.message}`);
                }
            }

            if (element) {
                testResult.elementInfo = {
                    tagName: element.tagName,
                    id: element.id,
                    className: element.className,
                    textContent: element.textContent?.substring(0, 100) || '',
                    boundingRect: element.getBoundingClientRect(),
                    isVisible: this.isElementVisible(element),
                    isClickable: this.isElementClickable(element)
                };
                
                testResult.clickable = testResult.elementInfo.isClickable;
            } else {
                testResult.errorDetails.push('Element not found with any targeting method');
            }

            return testResult;

        } catch (error) {
            testResult.errorDetails.push(`Click test error: ${error.message}`);
            return testResult;
        }
    }

    /**
     * Enhanced input event testing
     */
    async testInputEventEnhanced(event) {
        const testResult = {
            elementFound: false,
            elementInfo: null,
            targetingMethod: null,
            isInputField: false,
            canInput: false,
            errorDetails: []
        };

        try {
            const targetConfig = {
                selector: event.selector,
                alternatives: event.selectorAlternatives
            };
            
            const result = await this.findElementWithEnhancedTargeting(targetConfig);
            
            if (result.success) {
                const element = result.element;
                testResult.elementFound = true;
                testResult.targetingMethod = result.method;
                
                testResult.elementInfo = {
                    tagName: element.tagName,
                    id: element.id,
                    className: element.className,
                    type: element.type,
                    value: element.value,
                    placeholder: element.placeholder,
                    boundingRect: element.getBoundingClientRect(),
                    isVisible: this.isElementVisible(element),
                    isDisabled: element.disabled,
                    isReadonly: element.readOnly
                };
                
                // Check if it's an input field
                testResult.isInputField = this.isInputElement(element);
                testResult.canInput = testResult.isInputField && 
                                   !element.disabled && 
                                   !element.readOnly && 
                                   this.isElementVisible(element);
                                   
            } else {
                testResult.errorDetails = result.errorDetails;
            }

            return testResult;

        } catch (error) {
            testResult.errorDetails.push(`Input test error: ${error.message}`);
            return testResult;
        }
    }

    /**
     * Enhanced navigate event testing
     */
    async testNavigateEventEnhanced(event) {
        const testResult = {
            urlValid: false,
            isAbsoluteUrl: false,
            currentUrl: window.location.href,
            targetUrl: event.url,
            errorDetails: []
        };

        try {
            // Validate URL
            try {
                new URL(event.url);
                testResult.urlValid = true;
                testResult.isAbsoluteUrl = true;
            } catch (urlError) {
                // Try as relative URL
                try {
                    new URL(event.url, window.location.origin);
                    testResult.urlValid = true;
                    testResult.isAbsoluteUrl = false;
                } catch (relativeError) {
                    testResult.errorDetails.push(`Invalid URL: ${event.url}`);
                }
            }

            return testResult;

        } catch (error) {
            testResult.errorDetails.push(`Navigate test error: ${error.message}`);
            return testResult;
        }
    }

    /**
     * Enhanced wait event testing
     */
    async testWaitEventEnhanced(event) {
        const testResult = {
            validDuration: false,
            duration: event.duration,
            waitForElement: event.waitForElement,
            elementFound: null,
            errorDetails: []
        };

        try {
            // Validate duration
            if (event.duration) {
                testResult.validDuration = typeof event.duration === 'number' && event.duration > 0;
                if (!testResult.validDuration) {
                    testResult.errorDetails.push('Duration must be a positive number');
                }
            }

            // Test waitForElement if specified
            if (event.waitForElement) {
                try {
                    const element = await this.findElementByCSSSelector(event.waitForElement, 1000);
                    testResult.elementFound = !!element;
                } catch (error) {
                    testResult.elementFound = false;
                    testResult.errorDetails.push(`Wait element not found: ${error.message}`);
                }
            }

            return testResult;

        } catch (error) {
            testResult.errorDetails.push(`Wait test error: ${error.message}`);
            return testResult;
        }
    }

    /**
     * Enhanced text search click event testing
     */
    async testTextSearchClickEventEnhanced(event) {
        const testResult = {
            elementsFound: [],
            searchTexts: event.searchTexts || [],
            totalMatches: 0,
            errorDetails: []
        };

        try {
            for (const searchText of testResult.searchTexts) {
                try {
                    const elements = await this.findElementsByTextContent({
                        text: searchText,
                        partial: true,
                        caseSensitive: false,
                        elementTypes: event.elementTypes || ['button', 'a', 'span', 'div']
                    }, 2000);
                    
                    if (elements.length > 0) {
                        testResult.elementsFound.push({
                            searchText: searchText,
                            count: elements.length,
                            firstElement: {
                                tagName: elements[0].tagName,
                                textContent: elements[0].textContent?.substring(0, 50) || ''
                            }
                        });
                        testResult.totalMatches += elements.length;
                    }
                } catch (error) {
                    testResult.errorDetails.push(`Text search failed for "${searchText}": ${error.message}`);
                }
            }

            return testResult;

        } catch (error) {
            testResult.errorDetails.push(`Text search click test error: ${error.message}`);
            return testResult;
        }
    }

    /**
     * Enhanced extract event testing
     */
    async testExtractEventEnhanced(event) {
        const testResult = {
            elementFound: false,
            extractable: false,
            dataType: null,
            sampleData: null,
            errorDetails: []
        };

        try {
            if (event.selector) {
                const element = await this.findElementByCSSSelector(event.selector, 2000);
                
                if (element) {
                    testResult.elementFound = true;
                    testResult.extractable = true;
                    
                    // Determine data type and extract sample
                    if (element.value !== undefined) {
                        testResult.dataType = 'input_value';
                        testResult.sampleData = element.value?.substring(0, 100) || '';
                    } else if (element.textContent) {
                        testResult.dataType = 'text_content';
                        testResult.sampleData = element.textContent?.substring(0, 100) || '';
                    } else if (element.innerHTML) {
                        testResult.dataType = 'inner_html';
                        testResult.sampleData = element.innerHTML?.substring(0, 100) || '';
                    }
                }
            } else {
                testResult.errorDetails.push('Extract event requires selector');
            }

            return testResult;

        } catch (error) {
            testResult.errorDetails.push(`Extract test error: ${error.message}`);
            return testResult;
        }
    }

    /**
     * Generic event testing for unknown types
     */
    async testGenericEventEnhanced(event) {
        return {
            eventType: event.type,
            hasParameters: Object.keys(event).length > 1,
            parameters: event,
            note: 'Generic test - event type not specifically supported'
        };
    }

    /**
     * Check if element is clickable
     */
    isElementClickable(element) {
        if (!element || !this.isElementVisible(element)) {
            return false;
        }

        const style = window.getComputedStyle(element);
        
        // Check if element has pointer events disabled
        if (style.pointerEvents === 'none') {
            return false;
        }

        // Check if it's a naturally clickable element
        const clickableTags = ['button', 'a', 'input', 'select', 'textarea'];
        const isClickableTag = clickableTags.includes(element.tagName.toLowerCase());
        
        // Check if it has click event handlers or cursor pointer
        const hasClickHandlers = element.onclick || 
                                element.addEventListener || 
                                style.cursor === 'pointer';

        return isClickableTag || hasClickHandlers;
    }

    /**
     * Check if element is an input element
     */
    isInputElement(element) {
        const inputTags = ['input', 'textarea', 'select'];
        const contentEditableElements = element.contentEditable === 'true';
        
        return inputTags.includes(element.tagName.toLowerCase()) || contentEditableElements;
    }

    /**
     * Find multiple elements by text content
     */
    async findElementsByTextContent(textConfig, timeout = 3000) {
        const { text, partial = true, caseSensitive = false, elementTypes = ['*'] } = textConfig;
        const elements = [];
        
        const searchText = caseSensitive ? text : text.toLowerCase();
        
        for (const elementType of elementTypes) {
            const allElements = document.querySelectorAll(elementType);
            
            for (const element of allElements) {
                const elementText = caseSensitive ? 
                    element.textContent : 
                    element.textContent.toLowerCase();
                    
                const isMatch = partial ? 
                    elementText.includes(searchText) : 
                    elementText.trim() === searchText;
                    
                if (isMatch && this.isElementVisible(element)) {
                    elements.push(element);
                }
            }
        }
        
        return elements;
    }

    async extractPageData(selectors, sendResponse) {
        const results = {};
        
        for (const [key, selector] of Object.entries(selectors)) {
            try {
                const element = document.querySelector(selector);
                if (element) {
                    results[key] = element.textContent?.trim() || element.value || '';
                }
            } catch (error) {
                console.error(`Failed to extract data for ${key}:`, error);
                results[key] = null;
            }
        }
        
        sendResponse({ success: true, data: results });
    }

    getPageInfo() {
        return {
            url: window.location.href,
            title: document.title,
            readyState: document.readyState,
            timestamp: new Date().toISOString()
        };
    }

    generateExecutionId() {
        return 'exec_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
    }

    notifyProgress(progress, step) {
        chrome.runtime.sendMessage({
            action: 'executionProgress',
            progress: progress,
            step: step
        });
    }

    notifyCompletion(success, error = null, results = null) {
        chrome.runtime.sendMessage({
            action: 'executionComplete',
            success: success,
            error: error,
            results: results
        });
    }

    notifyInteraction(type, selector, success) {
        chrome.runtime.sendMessage({
            action: 'elementInteraction',
            type: type,
            selector: selector,
            success: success,
            status: success ? 'completed' : 'failed'
        });
    }

    notifyDataExtraction(dataPoints) {
        chrome.runtime.sendMessage({
            action: 'dataExtracted',
            dataPoints: dataPoints
        });
    }

    injectAdvancedHelpers() {
        // Inject the advanced automation helpers if not already present
        if (!window.venusAutoFillInjected) {
            const script = document.createElement('script');
            script.src = chrome.runtime.getURL('injected.js');
            script.onload = () => {
                console.log('✅ Advanced automation helpers injected');
            };
            (document.head || document.documentElement).appendChild(script);
        }

        // Inject text search engine
        if (!window.venusTextSearchInjected) {
            const textSearchScript = document.createElement('script');
            textSearchScript.src = chrome.runtime.getURL('scripts/text-search-engine.js');
            textSearchScript.onload = () => {
                console.log('✅ Text search engine injected');
                window.venusTextSearchInjected = true;
            };
            (document.head || document.documentElement).appendChild(textSearchScript);
        }
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // ================== NEW EVENT TYPE IMPLEMENTATIONS ==================

    async executeOpenToEvent(event) {
        if (!event.url) {
            throw new Error('open_to event requires URL');
        }

        console.log(`🌐 Opening URL: ${event.url}`);

        if (event.newTab) {
            // Open in new tab
            window.open(event.url, '_blank');
        } else {
            // Navigate current tab
            window.location.href = event.url;

            if (event.waitForLoad !== false) {
                await this.waitForNavigation(event.timeout || 30000);
            }
        }

        console.log(`✅ Successfully opened: ${event.url}`);
    }

    // ================== MESSAGE HANDLERS FOR NEW FUNCTIONALITY ==================

    async executePostLoginSequenceMessage(message, sendResponse) {
        try {
            console.log('🔄 Starting post-login automation sequence from message');

            const result = await this.executePostLoginSequence(message.config || {});
            sendResponse({ success: true, result: result });

        } catch (error) {
            console.error('❌ Post-login sequence message handler failed:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async executeTextSearchMessage(message, sendResponse) {
        try {
            console.log('🔍 Executing text search from message');

            const searchEvent = {
                type: 'text_search',
                searchText: message.searchText,
                caseSensitive: message.caseSensitive,
                highlightMatches: message.highlightMatches,
                showNavigationControls: message.showNavigationControls,
                maxMatches: message.maxMatches
            };

            const result = await this.executeTextSearchEvent(searchEvent);
            sendResponse({ success: true, results: result });

        } catch (error) {
            console.error('❌ Text search message handler failed:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async executeTextSearchNavigateMessage(message, sendResponse) {
        try {
            console.log('🔍 Executing text search navigation from message');

            const navEvent = {
                type: 'text_search_navigate',
                direction: message.direction
            };

            await this.executeTextSearchNavigateEvent(navEvent);
            sendResponse({ success: true });

        } catch (error) {
            console.error('❌ Text search navigation message handler failed:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async showTextSearchUIMessage(message, sendResponse) {
        try {
            console.log('🔍 Showing text search UI from message');

            // Initialize text search engine if not already done
            if (!window.venusTextSearch) {
                await this.initializeTextSearchEngine();
            }

            if (window.venusTextSearch) {
                window.venusTextSearch.showSearchUI();
                sendResponse({ success: true });
            } else {
                throw new Error('Text search engine not available');
            }

        } catch (error) {
            console.error('❌ Show text search UI message handler failed:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async testEventMessage(message, sendResponse) {
        try {
            console.log('🧪 Testing individual event from message');

            const result = await this.testEvent(message.event, message.index || 0);
            sendResponse({ success: true, result: result });

        } catch (error) {
            console.error('❌ Test event message handler failed:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async preflightCheckMessage(message, sendResponse) {
        try {
            console.log('🔍 Performing preflight check from message');

            const results = await this.preflightCheck(message.flowEvents);
            sendResponse({ success: true, results: results });

        } catch (error) {
            console.error('❌ Preflight check message handler failed:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async validateFlowMessage(message, sendResponse) {
        try {
            console.log('✅ Validating flow from message');

            // Basic validation on content script side
            const validation = {
                isValid: true,
                errors: [],
                warnings: []
            };

            if (!Array.isArray(message.flowEvents)) {
                validation.isValid = false;
                validation.errors.push('Flow events must be an array');
            } else {
                for (let i = 0; i < message.flowEvents.length; i++) {
                    const event = message.flowEvents[i];
                    if (!event.type) {
                        validation.isValid = false;
                        validation.errors.push(`Event ${i + 1}: Missing event type`);
                    }
                }
            }

            sendResponse({ success: true, validation: validation });

        } catch (error) {
            console.error('❌ Validate flow message handler failed:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    // ================== ENHANCED ELEMENT FINDING ==================

    async findElementAdvanced(selector, options = {}) {
        const {
            timeout = 5000,
            selectorType = 'css',
            waitForVisible = false,
            retryInterval = 100
        } = options;

        const startTime = Date.now();

        while (Date.now() - startTime < timeout) {
            try {
                let element;

                switch (selectorType) {
                    case 'css':
                        element = document.querySelector(selector);
                        break;
                    case 'xpath':
                        const xpathResult = document.evaluate(
                            selector,
                            document,
                            null,
                            XPathResult.FIRST_ORDERED_NODE_TYPE,
                            null
                        );
                        element = xpathResult.singleNodeValue;
                        break;
                    case 'text':
                        element = await this.findElementByTextAdvanced(selector, {
                            timeout: retryInterval,
                            caseSensitive: options.caseSensitive || false
                        });
                        break;
                    default:
                        element = document.querySelector(selector);
                }

                if (element) {
                    if (waitForVisible) {
                        const rect = element.getBoundingClientRect();
                        const isVisible = rect.width > 0 && rect.height > 0;
                        if (isVisible) {
                            return element;
                        }
                    } else {
                        return element;
                    }
                }

            } catch (error) {
                console.warn(`Element finding error: ${error.message}`);
            }

            await this.delay(retryInterval);
        }

        return null;
    }

    // ================== INDIVIDUAL EVENT TESTING ==================

    async testEvent(event, index) {
        try {
            console.log(`🧪 Testing individual event ${index}: ${event.type}`);

            const result = {
                success: false,
                eventName: event.name || `Event ${index + 1}`,
                eventType: event.type,
                notes: '',
                error: null
            };

            // Validate event structure
            if (!event.type) {
                result.error = 'Event missing type property';
                return result;
            }

            // Test based on event type
            switch (event.type) {
                case 'click':
                    result.success = await this.testClickEvent(event);
                    result.notes = event.selector ?
                        `Tested click on: ${event.selector}` :
                        'No selector specified';
                    break;

                case 'input':
                    result.success = await this.testInputEvent(event);
                    result.notes = event.selector ?
                        `Tested input to: ${event.selector}` :
                        'No selector specified';
                    break;

                case 'wait':
                    result.success = await this.testWaitEvent(event);
                    result.notes = `Tested wait: ${event.duration || 1000}ms`;
                    break;

                case 'navigate':
                case 'open_to':
                    result.success = await this.testNavigateEvent(event);
                    result.notes = event.url ?
                        `Tested navigation to: ${event.url}` :
                        'No URL specified';
                    break;

                case 'extract':
                    result.success = await this.testExtractEvent(event);
                    result.notes = event.selector ?
                        `Tested extraction from: ${event.selector}` :
                        'No selector specified';
                    break;

                case 'wait_for_element':
                    result.success = await this.testWaitForElementEvent(event);
                    result.notes = event.selector ?
                        `Tested wait for element: ${event.selector}` :
                        'No selector specified';
                    break;

                default:
                    result.success = true;
                    result.notes = `Event type "${event.type}" - basic validation passed`;
            }

            if (result.success) {
                console.log(`✅ Event test passed: ${result.eventName}`);
            } else {
                console.log(`❌ Event test failed: ${result.eventName} - ${result.error || 'Unknown error'}`);
            }

            return result;

        } catch (error) {
            console.error(`❌ Event test error for ${event.type}:`, error);
            return {
                success: false,
                eventName: event.name || `Event ${index + 1}`,
                eventType: event.type,
                error: error.message,
                notes: 'Test execution failed'
            };
        }
    }

    async testClickEvent(event) {
        try {
            if (!event.selector && !event.searchText) {
                return false;
            }

            let element;
            if (event.searchText) {
                element = await this.findElementByTextAdvanced(event.searchText, {
                    timeout: 2000,
                    caseSensitive: event.caseSensitive || false
                });
            } else {
                element = await this.findElementAdvanced(event.selector, {
                    timeout: 2000,
                    selectorType: event.selectorType || 'css'
                });
            }

            if (!element) {
                return false;
            }

            // Check if element is clickable
            const rect = element.getBoundingClientRect();
            const isVisible = rect.width > 0 && rect.height > 0;
            const isEnabled = !element.disabled;

            return isVisible && isEnabled;

        } catch (error) {
            console.error('Click event test error:', error);
            return false;
        }
    }

    async testInputEvent(event) {
        try {
            if (!event.selector) {
                return false;
            }

            const element = await this.findElementAdvanced(event.selector, {
                timeout: 2000,
                selectorType: event.selectorType || 'css'
            });

            if (!element) {
                return false;
            }

            // Check if element is an input field
            const isInputField = ['INPUT', 'TEXTAREA', 'SELECT'].includes(element.tagName) ||
                                element.contentEditable === 'true';

            const isEnabled = !element.disabled && !element.readOnly;

            return isInputField && isEnabled;

        } catch (error) {
            console.error('Input event test error:', error);
            return false;
        }
    }

    async testWaitEvent(event) {
        try {
            // Wait events are always valid if they have a duration or condition
            return !!(event.duration || event.waitFor);
        } catch (error) {
            console.error('Wait event test error:', error);
            return false;
        }
    }

    async testNavigateEvent(event) {
        try {
            if (!event.url) {
                return false;
            }

            // Basic URL validation
            try {
                new URL(event.url);
                return true;
            } catch (urlError) {
                return false;
            }

        } catch (error) {
            console.error('Navigate event test error:', error);
            return false;
        }
    }

    async testExtractEvent(event) {
        try {
            if (!event.selector) {
                return false;
            }

            const element = await this.findElementAdvanced(event.selector, {
                timeout: 2000,
                selectorType: event.selectorType || 'css'
            });

            if (!element) {
                return false;
            }

            // Check if we can extract the specified attribute
            const attribute = event.attribute || 'text';
            let value;

            switch (attribute) {
                case 'text':
                    value = element.textContent || element.innerText;
                    break;
                case 'value':
                    value = element.value;
                    break;
                case 'href':
                    value = element.href;
                    break;
                default:
                    value = element.getAttribute(attribute);
            }

            return value !== null && value !== undefined;

        } catch (error) {
            console.error('Extract event test error:', error);
            return false;
        }
    }

    async testWaitForElementEvent(event) {
        try {
            if (!event.selector) {
                return false;
            }

            const element = await this.findElementAdvanced(event.selector, {
                timeout: 1000, // Short timeout for testing
                selectorType: event.selectorType || 'css'
            });

            // For testing, we just check if the element exists
            // In actual execution, we would wait for it to appear/disappear
            return !!element;

        } catch (error) {
            console.error('Wait for element event test error:', error);
            return false;
        }
    }

    async preflightCheck(flowEvents) {
        try {
            console.log('🔍 Performing preflight check on current page');

            const results = {
                totalElements: 0,
                elementsFound: 0,
                missingElements: [],
                foundElements: []
            };

            for (const event of flowEvents) {
                if (event.selector) {
                    results.totalElements++;

                    try {
                        const element = await this.findElementAdvanced(event.selector, {
                            timeout: 1000,
                            selectorType: event.selectorType || 'css'
                        });

                        if (element) {
                            results.elementsFound++;
                            results.foundElements.push(event.selector);
                        } else {
                            results.missingElements.push(event.selector);
                        }
                    } catch (error) {
                        results.missingElements.push(event.selector);
                    }
                }
            }

            console.log(`🔍 Preflight check completed: ${results.elementsFound}/${results.totalElements} elements found`);
            return results;

        } catch (error) {
            console.error('❌ Preflight check error:', error);
            throw error;
        }
    }

    // ================== POST-LOGIN AUTOMATION SEQUENCE ==================

    async executePostLoginSequence(event) {
        console.log('🔄 Starting post-login automation sequence');

        try {
            // Step 1: Wait for page load completion after login
            console.log('⏳ Waiting for page load completion...');
            await this.waitForNavigation(event.pageLoadTimeout || 10000);

            // Step 2: Search for "ok" element and click it
            console.log('🔍 Searching for "ok" element...');
            const okElement = await this.findElementByTextAdvanced('ok', {
                timeout: event.okElementTimeout || 5000,
                caseSensitive: false,
                elementTypes: ['button', 'input', 'a', 'div', 'span']
            });

            if (okElement) {
                console.log('✅ Found "ok" element, adding delay...');
                await this.delay(event.okElementDelay || 200);

                // Click the ok element
                this.scrollElementIntoView(okElement);
                await this.delay(100);
                okElement.click();
                console.log('✅ Clicked "ok" element');
            } else {
                console.log('⚠️ "ok" element not found, continuing...');
            }

            // Step 3: Navigate to target page
            console.log('🌐 Navigating to target page...');
            const targetUrl = event.targetUrl || 'http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterList.aspx';
            window.location.href = targetUrl;

            // Wait for navigation
            await this.waitForNavigation(event.navigationTimeout || 15000);
            console.log('✅ Navigation completed');

            // Add delay after navigation
            await this.delay(event.navigationDelay || 300);

            // Step 4: Search for "new" element and click it
            console.log('🔍 Searching for "new" element...');
            const newElement = await this.findElementByTextAdvanced('new', {
                timeout: event.newElementTimeout || 10000,
                caseSensitive: false,
                elementTypes: ['button', 'input', 'a']
            });

            if (newElement) {
                console.log('✅ Found "new" element, clicking...');
                this.scrollElementIntoView(newElement);
                await this.delay(100);
                newElement.click();
                console.log('✅ Clicked "new" element');

                // Wait for any resulting page changes or modal dialogs
                await this.delay(event.newElementWaitAfter || 2000);
            } else {
                throw new Error('Required "new" element not found');
            }

            console.log('🎉 Post-login automation sequence completed successfully');

        } catch (error) {
            console.error('❌ Post-login automation sequence failed:', error);
            throw error;
        }
    }

    // ================== TEXT SEARCH FUNCTIONALITY ==================

    async executeTextSearchEvent(event) {
        console.log('🔍 Starting text search functionality');

        try {
            const searchOptions = {
                searchText: event.searchText || '',
                caseSensitive: event.caseSensitive || false,
                highlightMatches: event.highlightMatches !== false,
                showNavigationControls: event.showNavigationControls !== false,
                maxMatches: event.maxMatches || 100
            };

            // Initialize text search engine
            if (!window.venusTextSearch) {
                await this.initializeTextSearchEngine();
            }

            // Perform search
            const results = await window.venusTextSearch.search(searchOptions.searchText, searchOptions);

            console.log(`✅ Text search completed: ${results.matches.length} matches found`);

            // Store results for navigation
            this.extractedData.textSearchResults = results;

            return results;

        } catch (error) {
            console.error('❌ Text search failed:', error);
            throw error;
        }
    }

    async executeTextSearchNavigateEvent(event) {
        console.log('🔍 Navigating text search results');

        try {
            if (!this.extractedData.textSearchResults) {
                throw new Error('No text search results available. Run text_search first.');
            }

            const direction = event.direction || 'next'; // 'next' or 'previous'
            const results = this.extractedData.textSearchResults;

            if (results.matches.length === 0) {
                throw new Error('No search matches to navigate');
            }

            // Get current index or start from beginning
            let currentIndex = results.currentIndex || 0;

            if (direction === 'next') {
                currentIndex = (currentIndex + 1) % results.matches.length;
            } else if (direction === 'previous') {
                currentIndex = currentIndex === 0 ? results.matches.length - 1 : currentIndex - 1;
            }

            // Navigate to the match
            const match = results.matches[currentIndex];
            if (match && match.element) {
                this.scrollElementIntoView(match.element);

                // Highlight current match
                if (window.venusTextSearch) {
                    window.venusTextSearch.highlightMatch(currentIndex);
                }

                // Update current index
                results.currentIndex = currentIndex;

                console.log(`✅ Navigated to match ${currentIndex + 1}/${results.matches.length}`);
            }

        } catch (error) {
            console.error('❌ Text search navigation failed:', error);
            throw error;
        }
    }

    async initializeTextSearchEngine() {
        console.log('🔧 Initializing text search engine');

        // Create text search engine if not exists
        if (!window.venusTextSearch) {
            window.venusTextSearch = new VenusTextSearchEngine();
        }

        console.log('✅ Text search engine initialized');
    }

    async executeWaitForElementEvent(event) {
        if (!event.selector) {
            throw new Error('wait_for_element event requires selector');
        }

        const timeout = event.timeout || 10000;
        const expectVisible = event.expectVisible !== false; // Default to true
        
        console.log(`⏳ Waiting for element ${expectVisible ? 'to appear' : 'to disappear'}: ${event.selector}`);
        
        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            const element = document.querySelector(event.selector);
            const isVisible = element && this.isElementVisible(element);
            
            if (expectVisible && isVisible) {
                console.log(`✅ Element appeared: ${event.selector}`);
                return element;
            } else if (!expectVisible && !isVisible) {
                console.log(`✅ Element disappeared: ${event.selector}`);
                return;
            }
            
            await this.delay(100);
        }
        
        throw new Error(`Element ${expectVisible ? 'did not appear' : 'did not disappear'} within ${timeout}ms: ${event.selector}`);
    }

    async executeHoverEvent(event) {
        const element = await this.findElement(event.selector, event.selectorType);
        
        if (!element) {
            throw new Error(`Hover target not found: ${event.selector}`);
        }

        // Scroll to element if needed
        this.scrollElementIntoView(element);
        await this.delay(100);

        // Create and dispatch mouse events for hover
        const rect = element.getBoundingClientRect();
        const x = rect.left + rect.width / 2;
        const y = rect.top + rect.height / 2;

        const mouseEvents = ['mouseover', 'mouseenter', 'mousemove'];
        
        for (const eventType of mouseEvents) {
            element.dispatchEvent(new MouseEvent(eventType, {
                bubbles: true,
                cancelable: true,
                clientX: x,
                clientY: y
            }));
            await this.delay(50);
        }

        if (event.duration) {
            await this.delay(event.duration);
        }

        console.log(`✅ Hovered over element: ${event.selector}`);
    }

    async executeScrollToEvent(event) {
        if (event.selector) {
            // Scroll to specific element
            const element = await this.findElement(event.selector, event.selectorType);
            if (!element) {
                throw new Error(`Scroll target not found: ${event.selector}`);
            }
            
            element.scrollIntoView({
                behavior: event.smooth !== false ? 'smooth' : 'auto',
                block: event.block || 'center',
                inline: event.inline || 'center'
            });
        } else if (event.position) {
            // Scroll to specific position
            const { x = 0, y = 0 } = event.position;
            window.scrollTo({
                left: x,
                top: y,
                behavior: event.smooth !== false ? 'smooth' : 'auto'
            });
        } else {
            throw new Error('scroll_to event requires either selector or position');
        }

        await this.delay(event.waitAfter || 1000);
        console.log(`✅ Scrolled to target`);
    }

    async executeSelectOptionEvent(event) {
        const selectElement = await this.findElement(event.selector, event.selectorType);
        
        if (!selectElement || selectElement.tagName !== 'SELECT') {
            throw new Error(`Select element not found: ${event.selector}`);
        }

        // Scroll to element
        this.scrollElementIntoView(selectElement);
        await this.delay(100);

        // Find option by value, text, or index
        let optionElement = null;
        
        if (event.value !== undefined) {
            optionElement = selectElement.querySelector(`option[value="${event.value}"]`);
        } else if (event.text) {
            optionElement = Array.from(selectElement.options)
                .find(option => option.textContent.trim() === event.text);
        } else if (event.index !== undefined) {
            optionElement = selectElement.options[event.index];
        }

        if (!optionElement) {
            throw new Error(`Option not found in select ${event.selector}`);
        }

        // Select the option
        optionElement.selected = true;
        selectElement.value = optionElement.value;

        // Dispatch events
        selectElement.dispatchEvent(new Event('change', { bubbles: true }));
        selectElement.dispatchEvent(new Event('input', { bubbles: true }));

        console.log(`✅ Selected option: ${optionElement.textContent} (${optionElement.value})`);
    }

    async executeAlertHandleEvent(event) {
        const action = event.action || 'accept'; // accept, dismiss, text
        const timeout = event.timeout || 5000;
        
        console.log(`🚨 Waiting for alert to handle with action: ${action}`);
        
        // Set up alert handler
        const originalAlert = window.alert;
        const originalConfirm = window.confirm;
        const originalPrompt = window.prompt;
        
        let alertHandled = false;
        let alertResult = null;
        
        const handleAlert = (originalFn, defaultResult) => {
            return function(...args) {
                alertHandled = true;
                
                if (action === 'accept') {
                    alertResult = defaultResult;
                    return defaultResult;
                } else if (action === 'dismiss') {
                    alertResult = false;
                    return false;
                } else if (action === 'text' && event.text) {
                    alertResult = event.text;
                    return event.text;
                }
                
                return originalFn.apply(this, args);
            };
        };
        
        window.alert = handleAlert(originalAlert, true);
        window.confirm = handleAlert(originalConfirm, action === 'accept');
        window.prompt = handleAlert(originalPrompt, event.text || '');
        
        // Wait for alert or timeout
        const startTime = Date.now();
        while (!alertHandled && Date.now() - startTime < timeout) {
            await this.delay(100);
        }
        
        // Restore original functions
        window.alert = originalAlert;
        window.confirm = originalConfirm;
        window.prompt = originalPrompt;
        
        if (!alertHandled) {
            throw new Error(`No alert appeared within ${timeout}ms`);
        }
        
        console.log(`✅ Alert handled with result: ${alertResult}`);
    }

    async executeScreenshotEvent(event) {
        console.log(`📸 Taking screenshot: ${event.name || 'unnamed'}`);
        
        // Store screenshot metadata
        const screenshotData = {
            name: event.name || `screenshot_${Date.now()}`,
            timestamp: new Date().toISOString(),
            url: window.location.href,
            description: event.description
        };
        
        // Store in extracted data for reporting
        if (!this.extractedData.screenshots) {
            this.extractedData.screenshots = [];
        }
        this.extractedData.screenshots.push(screenshotData);
        
        console.log(`✅ Screenshot metadata recorded: ${screenshotData.name}`);
    }

    async executeFormFillEvent(event) {
        if (!event.fields || !Array.isArray(event.fields)) {
            throw new Error('form_fill event requires fields array');
        }

        console.log(`📝 Filling form with ${event.fields.length} fields`);
        
        for (const field of event.fields) {
            try {
                const element = await this.findElement(field.selector, field.selectorType || 'css');
                
                if (!element) {
                    if (field.required !== false) {
                        throw new Error(`Required form field not found: ${field.selector}`);
                    }
                    console.warn(`⚠️ Optional field not found: ${field.selector}`);
                    continue;
                }

                // Determine value
                let value = field.value;
                if (field.dataMapping && this.automationData) {
                    value = this.getDataValue(field.dataMapping) || field.value;
                }

                // Fill based on element type
                if (element.tagName === 'SELECT') {
                    await this.executeSelectOptionEvent({
                        selector: field.selector,
                        value: value,
                        text: field.text
                    });
                } else if (element.type === 'checkbox' || element.type === 'radio') {
                    element.checked = Boolean(value);
                    element.dispatchEvent(new Event('change', { bubbles: true }));
                } else {
                    // Regular input
                    await this.executeInputEvent({
                        selector: field.selector,
                        selectorType: field.selectorType,
                        value: value,
                        clearFirst: field.clearFirst
                    });
                }

                await this.delay(event.fieldDelay || 100);
            } catch (error) {
                console.error(`Error filling field ${field.selector}:`, error);
                if (field.required !== false) {
                    throw error;
                }
            }
        }

        console.log(`✅ Form filled successfully`);
    }

    async executeTabSwitchEvent(event) {
        if (event.tabIndex !== undefined) {
            // Switch by index (requires background script communication)
            console.log(`🔄 Switching to tab index: ${event.tabIndex}`);
            
            // Send message to background to switch tabs
            chrome.runtime.sendMessage({
                action: 'switchTab',
                tabIndex: event.tabIndex
            });
        } else if (event.url) {
            // Open URL in new tab
            window.open(event.url, '_blank');
            console.log(`🔄 Opened new tab with URL: ${event.url}`);
        } else {
            throw new Error('tab_switch event requires either tabIndex or url');
        }
    }

    async executeLoopEvent(event, currentIndex) {
        if (!event.iterations || event.iterations < 1) {
            throw new Error('loop event requires iterations > 0');
        }

        if (!event.events || !Array.isArray(event.events)) {
            throw new Error('loop event requires events array');
        }

        console.log(`🔄 Starting loop: ${event.iterations} iterations`);
        
        for (let i = 0; i < event.iterations; i++) {
            console.log(`🔄 Loop iteration ${i + 1}/${event.iterations}`);
            
            // Set loop variables
            this.extractedData.loopIndex = i;
            this.extractedData.loopIteration = i + 1;
            
            for (const loopEvent of event.events) {
                if (!this.isExecuting) {
                    throw new Error('Automation stopped during loop');
                }
                
                try {
                    await this.executeEvent(loopEvent, `${currentIndex}.${i}`);
                    await this.delay(event.eventDelay || 100);
                } catch (error) {
                    if (event.continueOnError) {
                        console.warn(`⚠️ Loop event failed but continuing: ${error.message}`);
                    } else {
                        throw error;
                    }
                }
            }
            
            if (i < event.iterations - 1) {
                await this.delay(event.iterationDelay || 500);
            }
        }

        console.log(`✅ Loop completed: ${event.iterations} iterations`);
    }

    async executeIfThenElseEvent(event, currentIndex) {
        if (!event.condition) {
            throw new Error('if_then_else event requires condition');
        }

        console.log(`🔍 Evaluating if-then-else condition: ${JSON.stringify(event.condition)}`);
        
        const conditionResult = await this.evaluateEventCondition(event.condition);
        
        let eventsToExecute = [];
        
        if (conditionResult && event.thenEvents) {
            console.log(`✅ Condition true, executing 'then' events`);
            eventsToExecute = event.thenEvents;
        } else if (!conditionResult && event.elseEvents) {
            console.log(`❌ Condition false, executing 'else' events`);
            eventsToExecute = event.elseEvents;
        } else {
            console.log(`⏭️ No matching events to execute`);
            return;
        }

        for (let i = 0; i < eventsToExecute.length; i++) {
            if (!this.isExecuting) {
                throw new Error('Automation stopped during conditional execution');
            }
            
            await this.executeEvent(eventsToExecute[i], `${currentIndex}.${conditionResult ? 'then' : 'else'}.${i}`);
            await this.delay(event.eventDelay || 100);
        }

        console.log(`✅ Conditional execution completed`);
    }

    async executeConditionalActionEvent(event, currentIndex) {
        if (!event.condition) {
            throw new Error('conditional_action event requires condition');
        }

        console.log(`🔍 Evaluating conditional_action condition: ${JSON.stringify(event.condition)}`);
        
        const conditionResult = await this.evaluateEventCondition(event.condition);
        
        if (conditionResult && event.true_action) {
            console.log(`✅ Condition true, executing true_action`);
            await this.executeSingleAction(event.true_action, `${currentIndex}.true`);
        } else if (conditionResult && event.thenEvents) {
            console.log(`✅ Condition true, executing 'then' events`);
            await this.executeEvents(event.thenEvents, `${currentIndex}.then`);
        } else if (!conditionResult && event.false_action) {
            console.log(`❌ Condition false, executing false_action`);
            await this.executeSingleAction(event.false_action, `${currentIndex}.false`);
        } else if (!conditionResult && event.elseEvents) {
            console.log(`❌ Condition false, executing 'else' events`);
            await this.executeEvents(event.elseEvents, `${currentIndex}.else`);
        } else {
            console.log(`⏭️ No matching events to execute`);
        }

        console.log(`✅ Conditional action completed`);
    }

    async executeSingleAction(action, actionIndex) {
        if (!action || !action.type) {
            console.log('⚠️ Invalid action provided');
            return;
        }

        console.log(`🎯 Executing single action: ${action.type}`);
        
        // Convert action to event format and execute
        const actionEvent = { ...action };
        await this.executeEvent(actionEvent, actionIndex);
        
        // Wait after action if specified
        if (action.wait_after_click || action.wait_after_action) {
            const waitTime = action.wait_after_click || action.wait_after_action;
            console.log(`⏳ Waiting ${waitTime}ms after action`);
            await this.delay(waitTime);
        }
    }

    async executeEvents(events, baseIndex) {
        if (!events || !Array.isArray(events)) {
            return;
        }

        for (let i = 0; i < events.length; i++) {
            if (!this.isExecuting) {
                throw new Error('Automation stopped during event execution');
            }
            
            await this.executeEvent(events[i], `${baseIndex}.${i}`);
            await this.delay(100); // Small delay between events
        }
    }

    async executeVariableSetEvent(event) {
        if (!event.variableName) {
            throw new Error('variable_set event requires variableName');
        }

        let value = event.value;
        
        // Support dynamic value setting
        if (event.dataMapping) {
            value = this.getDataValue(event.dataMapping) || value;
        } else if (event.selector) {
            // Extract value from element
            const element = await this.findElement(event.selector, event.selectorType);
            if (element) {
                value = element.textContent || element.value || element.getAttribute(event.attribute || 'value');
            }
        } else if (event.expression) {
            // Evaluate JavaScript expression safely
            try {
                value = this.evaluateExpression(event.expression);
            } catch (error) {
                console.warn(`⚠️ Expression evaluation failed: ${error.message}`);
            }
        }

        // Store variable
        this.extractedData[event.variableName] = value;
        this.executionResults.extractedData[event.variableName] = value;

        console.log(`✅ Variable set: ${event.variableName} = ${value}`);
    }

    async executeDataExtractMultipleEvent(event) {
        if (!event.extractions || !Array.isArray(event.extractions)) {
            throw new Error('data_extract_multiple event requires extractions array');
        }

        console.log(`📊 Extracting ${event.extractions.length} data points`);
        
        const extractedData = {};
        
        for (const extraction of event.extractions) {
            try {
                const element = await this.findElement(extraction.selector, extraction.selectorType);
                
                if (!element) {
                    if (extraction.required !== false) {
                        throw new Error(`Required extraction element not found: ${extraction.selector}`);
                    }
                    console.warn(`⚠️ Optional extraction element not found: ${extraction.selector}`);
                    continue;
                }

                let value;
                switch (extraction.attribute || 'text') {
                    case 'text':
                        value = element.textContent?.trim();
                        break;
                    case 'value':
                        value = element.value;
                        break;
                    case 'html':
                        value = element.innerHTML;
                        break;
                    default:
                        value = element.getAttribute(extraction.attribute);
                }

                // Apply transformation if specified
                if (extraction.transform) {
                    value = this.transformValue(value, extraction.transform);
                }

                extractedData[extraction.name] = value;
                
                if (extraction.storeGlobally) {
                    this.extractedData[extraction.name] = value;
                    this.executionResults.extractedData[extraction.name] = value;
                }

            } catch (error) {
                console.error(`Error extracting ${extraction.name}:`, error);
                if (extraction.required !== false) {
                    throw error;
                }
            }
        }

        console.log(`✅ Data extraction completed:`, extractedData);
        return extractedData;
    }

    async executeTextSearchClickEvent(event) {
        if (!event.searchText && !event.searchTexts) {
            throw new Error('text_search_click event requires searchText or searchTexts');
        }

        const searchTexts = event.searchTexts || [event.searchText];
        const timeout = event.timeout || 1000;
        
        console.log(`🔍 Text search click for: ${searchTexts.join(', ')}`);
        
        let element = null;
        
        // Try quick search first (optimized for speed)
        if (event.quick !== false) {
            for (const text of searchTexts) {
                element = await this.quickTextSearch(text, timeout / 2);
                if (element) break;
            }
        }
        
        // Fallback to advanced search if quick search fails
        if (!element) {
            console.log(`⚡ Quick search failed, trying advanced search...`);
            for (const text of searchTexts) {
                element = await this.findElementByTextAdvanced(text, {
                    timeout: timeout / 2,
                    caseSensitive: event.caseSensitive || false,
                    exactMatch: event.exactMatch || false,
                    elementTypes: event.elementTypes || ['button', 'input', 'a']
                });
                if (element) break;
            }
        }
        
        if (!element) {
            throw new Error(`Text search click failed: No element found with text "${searchTexts.join(', ')}"`);
        }

        // Scroll to element if needed
        this.scrollElementIntoView(element);
        await this.delay(100);

        // Click the element
        element.focus();
        element.click();

        // Dispatch additional events for better compatibility
        element.dispatchEvent(new MouseEvent('mousedown', { bubbles: true }));
        element.dispatchEvent(new MouseEvent('mouseup', { bubbles: true }));
        element.dispatchEvent(new MouseEvent('click', { bubbles: true }));

        console.log(`✅ Text search click completed: "${searchTexts[0]}"`);

        // Wait after click if specified
        if (event.wait_after_click) {
            console.log(`⏳ Waiting ${event.wait_after_click}ms after text search click`);
            await this.delay(event.wait_after_click);
        }
    }

    // ================== ENHANCED CONDITION EVALUATION ==================

    async evaluateEventCondition(condition) {
        try {
            switch (condition.type) {
                case 'element_exists':
                    return await this.waitForElementCondition(
                        condition.selector, 
                        condition.timeout || 5000, 
                        condition.visible !== false
                    );

                case 'element_text_contains':
                    const textElement = await this.waitForElementCondition(
                        condition.selector, 
                        condition.timeout || 3000, 
                        true
                    );
                    if (!textElement) return false;
                    const text = textElement.textContent || '';
                    return text.includes(condition.value);

                case 'url_contains':
                    return window.location.href.includes(condition.value);

                case 'variable_equals':
                    const varValue = this.extractedData[condition.variableName];
                    return varValue === condition.value;

                case 'page_title_contains':
                    return document.title.includes(condition.value);

                case 'element_count':
                    const elements = document.querySelectorAll(condition.selector);
                    const count = elements.length;
                    
                    switch (condition.operator) {
                        case 'equals': return count === condition.value;
                        case 'greater_than': return count > condition.value;
                        case 'less_than': return count < condition.value;
                        default: return count > 0;
                    }

                case 'custom_script':
                    // Execute custom JavaScript for complex conditions
                    return this.evaluateExpression(condition.script);

                default:
                    console.warn(`Unknown condition type: ${condition.type}`);
                    return false;
            }
        } catch (error) {
            console.error('Error evaluating condition:', error);
            return false;
        }
    }

    async waitForElementCondition(selector, timeout = 5000, mustBeVisible = true) {
        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            try {
                const element = document.querySelector(selector);
                
                if (element) {
                    if (!mustBeVisible || this.isElementVisible(element)) {
                        console.log(`✅ Element condition met: ${selector}`);
                        return element;
                    }
                }
                
                // Wait a bit before checking again
                await this.delay(100);
            } catch (error) {
                console.warn(`Error checking element condition: ${error.message}`);
                await this.delay(100);
            }
        }
        
        console.log(`⏰ Element condition timeout: ${selector} (${timeout}ms)`);
        return null;
    }

    // ================== UTILITY METHODS ==================

    transformValue(value, transform) {
        switch (transform.type) {
            case 'trim':
                return value?.toString().trim();
            case 'uppercase':
                return value?.toString().toUpperCase();
            case 'lowercase':
                return value?.toString().toLowerCase();
            case 'number':
                return parseFloat(value) || 0;
            case 'regex_extract':
                const match = value?.toString().match(new RegExp(transform.pattern));
                return match ? match[transform.group || 0] : null;
            default:
                return value;
        }
    }

    evaluateExpression(expression) {
        // Safe expression evaluation with limited scope
        const context = {
            data: this.automationData,
            extracted: this.extractedData,
            url: window.location.href,
            title: document.title,
            Math: Math,
            Date: Date
        };
        
        try {
            const func = new Function(...Object.keys(context), `return ${expression}`);
            return func(...Object.values(context));
        } catch (error) {
            console.error('Expression evaluation error:', error);
            throw error;
        }
    }

    /**
     * Execute single event from JSON flow format
     */
    async executeSingleEvent(event, index, sendResponse) {
        try {
            console.log(`🚀 Executing single event ${index}: ${event.name || event.type}`);
            
            // Convert JSON flow event to execution format
            const executionEvent = this.convertJsonEventToExecution(event);
            
            // Execute the event
            await this.executeEvent(executionEvent, index);
            
            console.log(`✅ Single event executed successfully: ${event.name}`);
            sendResponse({
                success: true,
                message: `Event executed: ${event.name}`,
                eventType: event.type,
                eventName: event.name
            });
            
        } catch (error) {
            console.error(`❌ Single event execution failed:`, error);
            sendResponse({
                success: false,
                error: error.message,
                eventType: event.type,
                eventName: event.name
            });
        }
    }

    /**
     * Convert JSON flow event to execution format
     */
    convertJsonEventToExecution(jsonEvent) {
        const executionEvent = {
            type: jsonEvent.type,
            description: jsonEvent.description || jsonEvent.name,
            id: jsonEvent.id,
            name: jsonEvent.name
        };

        // Map parameters based on event type
        if (jsonEvent.parameters) {
            const params = jsonEvent.parameters;
            
            switch (jsonEvent.type) {
                case 'navigate':
                    executionEvent.url = params.url;
                    executionEvent.timeout = params.timeout;
                    break;
                    
                case 'wait':
                    executionEvent.duration = params.duration;
                    executionEvent.waitForElement = params.wait_for_element;
                    break;
                    
                case 'input':
                    executionEvent.selector = params.selector;
                    executionEvent.value = params.value;
                    executionEvent.clearFirst = params.clear_first;
                    executionEvent.simulateTyping = params.simulate_typing;
                    executionEvent.typingDelay = params.typing_delay;
                    break;
                    
                case 'click':
                    executionEvent.selector = params.selector;
                    executionEvent.clickMethod = params.click_method;
                    executionEvent.doubleClick = params.double_click;
                    executionEvent.waitAfterClick = params.wait_after_click;
                    break;
                    
                case 'text_search_click':
                    executionEvent.searchTexts = params.searchTexts;
                    executionEvent.timeout = params.timeout;
                    executionEvent.quick = params.quick;
                    executionEvent.elementTypes = params.elementTypes;
                    break;
                    
                case 'conditional_action':
                    executionEvent.condition = jsonEvent.condition;
                    executionEvent.trueAction = jsonEvent.true_action;
                    executionEvent.falseAction = jsonEvent.false_action;
                    break;
            }
            
            // Add selector alternatives if available
            if (params.selector_alternatives) {
                executionEvent.selectorAlternatives = params.selector_alternatives;
            }
        }

        return executionEvent;
    }

    /**
     * Run automation sequence from JSON flow format
     */
    async runAutomationSequence(message, sendResponse) {
        try {
            console.log('🚀 Starting automation sequence from JSON flow');
            
            const { steps, config } = message;
            
            if (!steps || !Array.isArray(steps)) {
                throw new Error('Invalid steps provided');
            }
            
            // Initialize execution
            this.isExecuting = true;
            this.isPaused = false;
            
            let executedSteps = 0;
            const results = [];
            
            // Send initial response
            sendResponse({ success: true, message: 'Automation sequence started' });
            
            // Execute each step
            for (let i = 0; i < steps.length; i++) {
                if (!this.isExecuting || this.isPaused) {
                    console.log('⏸️ Automation paused or stopped');
                    break;
                }
                
                const step = steps[i];
                console.log(`🎯 Executing step ${i + 1}/${steps.length}: ${step.type}`);
                
                try {
                    await this.executeEvent(step, i);
                    executedSteps++;
                    results.push({
                        step: i,
                        type: step.type,
                        status: 'success',
                        message: `${step.type} executed successfully`
                    });
                    
                    // Add delay between steps
                    const delay = config?.delayBetweenSteps || 1000;
                    await this.delay(delay);
                    
                } catch (error) {
                    console.error(`❌ Step ${i + 1} failed:`, error);
                    results.push({
                        step: i,
                        type: step.type,
                        status: 'error',
                        message: error.message
                    });
                    
                    // Continue or stop based on configuration
                    if (config?.stopOnError !== false) {
                        throw error;
                    }
                }
            }
            
            console.log(`✅ Automation sequence completed: ${executedSteps}/${steps.length} steps`);
            
            // Notify completion
            this.notifyCompletion(true, null, {
                executedSteps,
                totalSteps: steps.length,
                results
            });
            
        } catch (error) {
            console.error('❌ Automation sequence failed:', error);
            this.notifyCompletion(false, error.message, { error: error.message });
        } finally {
            this.isExecuting = false;
        }
    }

    // ================== ENHANCED ELEMENT TARGETING SYSTEM ==================

    /**
     * Enhanced element finding with multiple targeting methods
     */
    async findElementWithEnhancedTargeting(targetConfig, options = {}) {
        const { timeout = 5000, verbose = false } = options;
        let element = null;
        let method = null;
        let errorDetails = [];

        const startTime = Date.now();

        try {
            // Method 1: CSS Selector
            if (targetConfig.selector && !element) {
                try {
                    element = await this.findElementByCSSSelector(targetConfig.selector, timeout / 4);
                    if (element) method = 'css_selector';
                } catch (error) {
                    errorDetails.push(`CSS Selector failed: ${error.message}`);
                }
            }

            // Method 2: XPath
            if (targetConfig.xpath && !element) {
                try {
                    element = await this.findElementByXPath(targetConfig.xpath, timeout / 4);
                    if (element) method = 'xpath';
                } catch (error) {
                    errorDetails.push(`XPath failed: ${error.message}`);
                }
            }

            // Method 3: Text-based targeting
            if (targetConfig.text && !element) {
                try {
                    element = await this.findElementByTextContent(targetConfig.text, timeout / 4);
                    if (element) method = 'text_content';
                } catch (error) {
                    errorDetails.push(`Text targeting failed: ${error.message}`);
                }
            }

            // Method 4: Attribute-based targeting
            if (targetConfig.attribute && !element) {
                try {
                    element = await this.findElementByAttribute(targetConfig.attribute, timeout / 4);
                    if (element) method = 'attribute';
                } catch (error) {
                    errorDetails.push(`Attribute targeting failed: ${error.message}`);
                }
            }

            // Method 5: Alternative selectors
            if (targetConfig.alternatives && !element) {
                for (const altSelector of targetConfig.alternatives) {
                    try {
                        element = await this.findElementByCSSSelector(altSelector, timeout / 8);
                        if (element) {
                            method = 'alternative_selector';
                            break;
                        }
                    } catch (error) {
                        errorDetails.push(`Alternative ${altSelector} failed: ${error.message}`);
                    }
                }
            }

            const endTime = Date.now();
            const duration = endTime - startTime;

            if (verbose) {
                console.log(`🎯 Element targeting completed in ${duration}ms using ${method || 'none'}`);
                if (errorDetails.length > 0) {
                    console.log('Targeting errors:', errorDetails);
                }
            }

            return {
                element,
                method,
                duration,
                errorDetails,
                success: !!element
            };

        } catch (error) {
            console.error('Enhanced targeting failed:', error);
            return {
                element: null,
                method: null,
                duration: Date.now() - startTime,
                errorDetails: [...errorDetails, error.message],
                success: false
            };
        }
    }

    /**
     * Find element by CSS selector with enhanced waiting
     */
    async findElementByCSSSelector(selector, timeout = 3000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();

            const checkElement = () => {
                try {
                    const element = document.querySelector(selector);
                    if (element && this.isElementVisible(element)) {
                        resolve(element);
                        return;
                    }

                    if (Date.now() - startTime > timeout) {
                        reject(new Error(`CSS selector timeout: ${selector}`));
                        return;
                    }

                    setTimeout(checkElement, 100);
                } catch (error) {
                    reject(new Error(`Invalid CSS selector: ${selector} - ${error.message}`));
                }
            };

            checkElement();
        });
    }

    /**
     * Find element by XPath
     */
    async findElementByXPath(xpath, timeout = 3000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();

            const checkElement = () => {
                try {
                    const result = document.evaluate(
                        xpath,
                        document,
                        null,
                        XPathResult.FIRST_ORDERED_NODE_TYPE,
                        null
                    );
                    
                    const element = result.singleNodeValue;
                    if (element && this.isElementVisible(element)) {
                        resolve(element);
                        return;
                    }

                    if (Date.now() - startTime > timeout) {
                        reject(new Error(`XPath timeout: ${xpath}`));
                        return;
                    }

                    setTimeout(checkElement, 100);
                } catch (error) {
                    reject(new Error(`Invalid XPath: ${xpath} - ${error.message}`));
                }
            };

            checkElement();
        });
    }

    /**
     * Find element by text content
     */
    async findElementByTextContent(textConfig, timeout = 3000) {
        const { text, partial = true, caseSensitive = false, elementTypes = ['*'] } = textConfig;
        
        return new Promise((resolve, reject) => {
            const startTime = Date.now();

            const checkElement = () => {
                try {
                    const searchText = caseSensitive ? text : text.toLowerCase();
                    
                    for (const elementType of elementTypes) {
                        const elements = document.querySelectorAll(elementType);
                        
                        for (const element of elements) {
                            const elementText = caseSensitive ? 
                                element.textContent : 
                                element.textContent.toLowerCase();
                                
                            const isMatch = partial ? 
                                elementText.includes(searchText) : 
                                elementText.trim() === searchText;
                                
                            if (isMatch && this.isElementVisible(element)) {
                                resolve(element);
                                return;
                            }
                        }
                    }

                    if (Date.now() - startTime > timeout) {
                        reject(new Error(`Text content timeout: ${text}`));
                        return;
                    }

                    setTimeout(checkElement, 100);
                } catch (error) {
                    reject(new Error(`Text content search failed: ${error.message}`));
                }
            };

            checkElement();
        });
    }

    /**
     * Find element by attribute
     */
    async findElementByAttribute(attributeConfig, timeout = 3000) {
        const { name, value, partial = false } = attributeConfig;
        
        return new Promise((resolve, reject) => {
            const startTime = Date.now();

            const checkElement = () => {
                try {
                    let selector = `[${name}`;
                    if (value !== undefined) {
                        if (partial) {
                            selector += `*="${value}"`;
                        } else {
                            selector += `="${value}"`;
                        }
                    }
                    selector += ']';

                    const element = document.querySelector(selector);
                    if (element && this.isElementVisible(element)) {
                        resolve(element);
                        return;
                    }

                    if (Date.now() - startTime > timeout) {
                        reject(new Error(`Attribute timeout: ${name}=${value}`));
                        return;
                    }

                    setTimeout(checkElement, 100);
                } catch (error) {
                    reject(new Error(`Attribute search failed: ${error.message}`));
                }
            };

            checkElement();
        });
    }

    /**
     * Visual element confirmation - highlight/blink element
     */
    async highlightElement(message, sendResponse) {
        try {
            const { targetConfig, highlightOptions = {} } = message;
            const {
                duration = 2000,
                blinkCount = 3,
                highlightColor = '#ff0000',
                borderWidth = '3px',
                borderStyle = 'solid'
            } = highlightOptions;

            // Find the element
            const result = await this.findElementWithEnhancedTargeting(targetConfig, { verbose: true });
            
            if (!result.success) {
                sendResponse({
                    success: false,
                    error: 'Element not found',
                    details: result.errorDetails
                });
                return;
            }

            const element = result.element;

            // Store original styles
            const originalBorder = element.style.border;
            const originalBoxShadow = element.style.boxShadow;

            // Apply highlight styles
            const highlightStyle = `${borderWidth} ${borderStyle} ${highlightColor}`;
            const shadowStyle = `0 0 10px ${highlightColor}`;

            // Blink animation
            let blinkCounter = 0;
            const blinkInterval = setInterval(() => {
                if (blinkCounter % 2 === 0) {
                    element.style.border = highlightStyle;
                    element.style.boxShadow = shadowStyle;
                } else {
                    element.style.border = originalBorder;
                    element.style.boxShadow = originalBoxShadow;
                }
                
                blinkCounter++;
                if (blinkCounter >= blinkCount * 2) {
                    clearInterval(blinkInterval);
                    // Restore original styles
                    element.style.border = originalBorder;
                    element.style.boxShadow = originalBoxShadow;
                }
            }, duration / (blinkCount * 2));

            // Scroll element into view
            this.scrollElementIntoView(element);

            sendResponse({
                success: true,
                method: result.method,
                elementInfo: {
                    tagName: element.tagName,
                    id: element.id,
                    className: element.className,
                    textContent: element.textContent?.substring(0, 100) || '',
                    boundingRect: element.getBoundingClientRect()
                }
            });

        } catch (error) {
            console.error('Element highlighting failed:', error);
            sendResponse({
                success: false,
                error: error.message
            });
        }
    }

    /**
     * Validate selector and provide detailed feedback
     */
    async validateSelector(message, sendResponse) {
        try {
            const { targetConfig } = message;
            const result = await this.findElementWithEnhancedTargeting(targetConfig, { verbose: true });

            const validation = {
                isValid: result.success,
                method: result.method,
                duration: result.duration,
                errors: result.errorDetails
            };

            if (result.success) {
                const element = result.element;
                validation.elementInfo = {
                    tagName: element.tagName,
                    id: element.id,
                    className: element.className,
                    attributes: Array.from(element.attributes).map(attr => ({
                        name: attr.name,
                        value: attr.value
                    })),
                    textContent: element.textContent?.trim()?.substring(0, 200) || '',
                    innerHTML: element.innerHTML?.substring(0, 300) || '',
                    boundingRect: element.getBoundingClientRect(),
                    isVisible: this.isElementVisible(element),
                    styles: window.getComputedStyle(element)
                };

                // Generate alternative selectors
                validation.alternativeSelectors = this.generateAlternativeSelectors(element);
            }

            sendResponse({
                success: true,
                validation
            });

        } catch (error) {
            console.error('Selector validation failed:', error);
            sendResponse({
                success: false,
                error: error.message
            });
        }
    }

    /**
     * Generate alternative selectors for an element
     */
    generateAlternativeSelectors(element) {
        const alternatives = [];

        // ID-based selector
        if (element.id) {
            alternatives.push({
                type: 'id',
                selector: `#${element.id}`,
                specificity: 'high',
                description: 'ID-based selector'
            });
        }

        // Class-based selector
        if (element.className) {
            const classes = element.className.trim().split(/\s+/);
            alternatives.push({
                type: 'class',
                selector: `.${classes.join('.')}`,
                specificity: 'medium',
                description: 'Class-based selector'
            });
        }

        // Data attribute selectors
        Array.from(element.attributes).forEach(attr => {
            if (attr.name.startsWith('data-')) {
                alternatives.push({
                    type: 'data_attribute',
                    selector: `[${attr.name}="${attr.value}"]`,
                    specificity: 'medium',
                    description: `Data attribute: ${attr.name}`
                });
            }
        });

        // Tag + attribute combinations
        if (element.name) {
            alternatives.push({
                type: 'name_attribute',
                selector: `${element.tagName.toLowerCase()}[name="${element.name}"]`,
                specificity: 'medium',
                description: 'Tag + name attribute'
            });
        }

        // Position-based selector (nth-child)
        const siblings = Array.from(element.parentElement?.children || []);
        const position = siblings.indexOf(element) + 1;
        if (element.parentElement) {
            alternatives.push({
                type: 'nth_child',
                selector: `${element.tagName.toLowerCase()}:nth-child(${position})`,
                specificity: 'low',
                description: `Position-based (${position}th child)`
            });
        }

        // XPath
        alternatives.push({
            type: 'xpath',
            selector: this.generateXPathForElement(element),
            specificity: 'high',
            description: 'XPath selector'
        });

        return alternatives;
    }

    /**
     * Generate XPath for an element
     */
    generateXPathForElement(element) {
        const parts = [];
        let current = element;

        while (current && current.nodeType === Node.ELEMENT_NODE) {
            let part = current.tagName.toLowerCase();
            
            if (current.id) {
                part += `[@id='${current.id}']`;
                parts.unshift(part);
                break;
            }
            
            const siblings = Array.from(current.parentElement?.children || [])
                .filter(el => el.tagName === current.tagName);
            
            if (siblings.length > 1) {
                const index = siblings.indexOf(current) + 1;
                part += `[${index}]`;
            }
            
            parts.unshift(part);
            current = current.parentElement;
        }

        return '//' + parts.join('/');
    }

    /**
     * Get detailed element information
     */
    async getElementInfo(message, sendResponse) {
        try {
            const { targetConfig } = message;
            const result = await this.findElementWithEnhancedTargeting(targetConfig);

            if (!result.success) {
                sendResponse({
                    success: false,
                    error: 'Element not found',
                    details: result.errorDetails
                });
                return;
            }

            const element = result.element;
            const elementInfo = {
                tagName: element.tagName,
                id: element.id,
                className: element.className,
                attributes: {},
                textContent: element.textContent?.trim() || '',
                innerHTML: element.innerHTML,
                outerHTML: element.outerHTML,
                boundingRect: element.getBoundingClientRect(),
                isVisible: this.isElementVisible(element),
                computedStyles: {},
                parentInfo: null,
                childrenCount: element.children.length,
                siblingPosition: Array.from(element.parentElement?.children || []).indexOf(element) + 1
            };

            // Collect attributes
            Array.from(element.attributes).forEach(attr => {
                elementInfo.attributes[attr.name] = attr.value;
            });

            // Collect key computed styles
            const computedStyle = window.getComputedStyle(element);
            const styleProperties = [
                'display', 'visibility', 'position', 'width', 'height',
                'background-color', 'color', 'font-size', 'z-index'
            ];
            
            styleProperties.forEach(prop => {
                elementInfo.computedStyles[prop] = computedStyle.getPropertyValue(prop);
            });

            // Parent information
            if (element.parentElement) {
                elementInfo.parentInfo = {
                    tagName: element.parentElement.tagName,
                    id: element.parentElement.id,
                    className: element.parentElement.className
                };
            }

            sendResponse({
                success: true,
                elementInfo,
                targetingMethod: result.method
            });

        } catch (error) {
            console.error('Get element info failed:', error);
            sendResponse({
                success: false,
                error: error.message
            });
        }
    }

    /**
     * Generate selector alternatives for element
     */
    async generateSelectorAlternatives(message, sendResponse) {
        try {
            const { targetConfig } = message;
            const result = await this.findElementWithEnhancedTargeting(targetConfig);

            if (!result.success) {
                sendResponse({
                    success: false,
                    error: 'Element not found for alternative generation',
                    details: result.errorDetails
                });
                return;
            }

            const alternatives = this.generateAlternativeSelectors(result.element);

            // Test each alternative selector
            const testedAlternatives = await Promise.all(
                alternatives.map(async (alt) => {
                    try {
                        const testElement = await this.findElementByCSSSelector(alt.selector, 1000);
                        return {
                            ...alt,
                            isValid: !!testElement,
                            matches: testElement === result.element
                        };
                    } catch (error) {
                        return {
                            ...alt,
                            isValid: false,
                            matches: false,
                            error: error.message
                        };
                    }
                })
            );

            sendResponse({
                success: true,
                alternatives: testedAlternatives,
                originalMethod: result.method
            });

        } catch (error) {
            console.error('Generate selector alternatives failed:', error);
            sendResponse({
                success: false,
                error: error.message
            });
        }
    }

    /**
     * Find elements by text content
     */
    async findElementsByText(message, sendResponse) {
        try {
            const { 
                text, 
                options = {
                    partial: true,
                    caseSensitive: false,
                    elementTypes: ['button', 'a', 'span', 'div', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'],
                    maxResults: 10
                }
            } = message;

            const searchText = options.caseSensitive ? text : text.toLowerCase();
            const results = [];

            for (const elementType of options.elementTypes) {
                if (results.length >= options.maxResults) break;

                const elements = document.querySelectorAll(elementType);
                
                for (const element of elements) {
                    if (results.length >= options.maxResults) break;

                    const elementText = options.caseSensitive ? 
                        element.textContent : 
                        element.textContent.toLowerCase();

                    const isMatch = options.partial ? 
                        elementText.includes(searchText) : 
                        elementText.trim() === searchText;

                    if (isMatch && this.isElementVisible(element)) {
                        results.push({
                            element: {
                                tagName: element.tagName,
                                id: element.id,
                                className: element.className,
                                textContent: element.textContent.trim(),
                                boundingRect: element.getBoundingClientRect()
                            },
                            selector: this.generateBestSelector(element),
                            xpath: this.generateXPathForElement(element),
                            matchType: options.partial ? 'partial' : 'exact'
                        });
                    }
                }
            }

            sendResponse({
                success: true,
                results: results,
                searchText: text,
                totalFound: results.length
            });

        } catch (error) {
            console.error('Find elements by text failed:', error);
            sendResponse({
                success: false,
                error: error.message
            });
        }
    }

    /**
     * Generate the best selector for an element
     */
    generateBestSelector(element) {
        // Priority: ID > unique class > data attributes > tag + attributes
        if (element.id) {
            return `#${element.id}`;
        }

        if (element.className) {
            const classes = element.className.trim().split(/\s+/);
            const classSelector = `.${classes.join('.')}`;
            
            // Check if class combination is unique
            if (document.querySelectorAll(classSelector).length === 1) {
                return classSelector;
            }
        }

        // Try data attributes
        for (const attr of element.attributes) {
            if (attr.name.startsWith('data-')) {
                const selector = `[${attr.name}="${attr.value}"]`;
                if (document.querySelectorAll(selector).length === 1) {
                    return selector;
                }
            }
        }

        // Fallback to tag + attributes
        let selector = element.tagName.toLowerCase();
        
        if (element.name) {
            selector += `[name="${element.name}"]`;
        } else if (element.type) {
            selector += `[type="${element.type}"]`;
        }

        return selector;
    }

    showElementManagerInterface() {
        // Implement the showElementManagerInterface method
        console.log('Show Element Manager Interface');
    }

    /**
     * Detect and handle popup dialogs that may block automation
     */
    async executePopupHandlerEvent(event) {
        console.log('🔍 Detecting and handling popup dialogs...');
        
        try {
            const popupSelectors = event.popupSelectors || [
                '.PopupBoxLogin',
                '[class*="popup"]',
                '[class*="modal"]',
                '[id*="popup"]',
                '[id*="modal"]'
            ];
            
            const timeout = event.timeout || 5000;
            const startTime = Date.now();
            
            // Wait for popup to appear
            let popupFound = false;
            let popupElement = null;
            
            while (Date.now() - startTime < timeout && !popupFound) {
                for (const selector of popupSelectors) {
                    const element = document.querySelector(selector);
                    if (element && this.isElementVisible(element)) {
                        popupElement = element;
                        popupFound = true;
                        console.log(`✅ Popup detected with selector: ${selector}`);
                        break;
                    }
                }
                
                if (!popupFound) {
                    await this.delay(100);
                }
            }
            
            if (popupFound && popupElement) {
                // Handle the popup
                await this.handleDetectedPopup(popupElement, event);
                return { success: true, popupHandled: true };
            } else {
                console.log('⚠️ No popup detected within timeout');
                return { success: true, popupHandled: false };
            }
            
        } catch (error) {
            console.error('❌ Popup handler failed:', error);
            throw error;
        }
    }

    /**
     * Handle detected popup dialog
     */
    async handleDetectedPopup(popupElement, event) {
        console.log('🎯 Handling detected popup dialog');
        
        try {
            // Wait for popup to be fully loaded and interactive
            await this.delay(event.popupStabilizeDelay || 500);
            
            // Look for the OK button within the popup context
            const okButtonSelectors = event.okButtonSelectors || [
                '#MainContent_btnOkay',
                '.button',
                'input[type="button"][value*="OK"]',
                'input[type="button"][value*="Ok"]',
                'button:contains("OK")',
                'button:contains("Ok")'
            ];
            
            let okButton = null;
            
            // Try to find OK button within popup context first
            for (const selector of okButtonSelectors) {
                okButton = popupElement.querySelector(selector);
                if (okButton && this.isElementVisible(okButton)) {
                    console.log(`✅ OK button found within popup: ${selector}`);
                    break;
                }
            }
            
            // If not found in popup, search globally
            if (!okButton) {
                for (const selector of okButtonSelectors) {
                    okButton = document.querySelector(selector);
                    if (okButton && this.isElementVisible(okButton)) {
                        console.log(`✅ OK button found globally: ${selector}`);
                        break;
                    }
                }
            }
            
            if (okButton) {
                // Ensure button is clickable
                await this.ensureElementClickable(okButton);
                
                // Click the OK button
                console.log('🖱️ Clicking OK button to dismiss popup');
                this.scrollElementIntoView(okButton);
                await this.delay(200);
                
                // Try multiple click methods for reliability
                okButton.focus();
                await this.delay(100);
                okButton.click();
                
                // Dispatch additional events for better compatibility
                okButton.dispatchEvent(new MouseEvent('mousedown', { bubbles: true }));
                okButton.dispatchEvent(new MouseEvent('mouseup', { bubbles: true }));
                okButton.dispatchEvent(new MouseEvent('click', { bubbles: true }));
                
                // Wait for popup to dismiss
                await this.waitForPopupDismissal(popupElement, event.dismissalTimeout || 3000);
                
                console.log('✅ Popup handled successfully');
            } else {
                throw new Error('OK button not found in popup');
            }
            
        } catch (error) {
            console.error('❌ Failed to handle popup:', error);
            throw error;
        }
    }

    /**
     * Ensure element is clickable
     */
    async ensureElementClickable(element) {
        const maxAttempts = 10;
        let attempts = 0;
        
        while (attempts < maxAttempts) {
            if (this.isElementClickable(element) && this.isElementVisible(element)) {
                return true;
            }
            
            await this.delay(200);
            attempts++;
        }
        
        throw new Error('Element did not become clickable within timeout');
    }

    /**
     * Wait for popup to be dismissed
     */
    async waitForPopupDismissal(popupElement, timeout = 3000) {
        console.log('⏳ Waiting for popup dismissal...');
        
        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            if (!this.isElementVisible(popupElement)) {
                console.log('✅ Popup dismissed successfully');
                return true;
            }
            await this.delay(100);
        }
        
        // Check if popup is still visible but perhaps moved/changed
        const isStillVisible = this.isElementVisible(popupElement);
        if (isStillVisible) {
            console.warn('⚠️ Popup may still be visible after timeout');
        }
        
        return !isStillVisible;
    }

    /**
     * Execute wait for page stability event
     */
    async executeWaitForPageStabilityEvent(event) {
        console.log('⏳ Waiting for page stability...');
        
        const timeout = event.timeout || 3000;
        const maxWait = event.maxWait || 10000;
        
        try {
            await this.waitForPageStability(timeout);
            console.log('✅ Page stability achieved');
            
            // Additional wait if specified
            if (event.stabilityDelay) {
                await this.delay(event.stabilityDelay);
            }
            
        } catch (error) {
            console.warn('⚠️ Page stability timeout, continuing anyway');
        }
    }

    /**
     * Execute detect and click popup OK event
     */
    async executeDetectAndClickPopupOkEvent(event) {
        console.log('🔍 Detecting popup and clicking OK button...');
        
        const popupSelector = event.popupSelector || '.PopupBoxLogin';
        const okButtonSelector = event.okButtonSelector || '#MainContent_btnOkay';
        const timeout = event.timeout || 10000;
        const waitAfterClick = event.waitAfterClick || 1000;
        
        const startTime = Date.now();
        
        try {
            // Wait for popup to appear
            console.log(`Looking for popup: ${popupSelector}`);
            let popupElement = null;
            
            while (Date.now() - startTime < timeout) {
                popupElement = document.querySelector(popupSelector);
                if (popupElement && this.isElementVisible(popupElement)) {
                    console.log('✅ Popup detected and visible');
                    break;
                }
                await this.delay(100);
            }
            
            if (!popupElement || !this.isElementVisible(popupElement)) {
                console.log('⚠️ No popup found, continuing anyway');
                return;
            }
            
            // Wait a bit for popup to stabilize
            await this.delay(500);
            
            // Find and click OK button
            console.log(`Looking for OK button: ${okButtonSelector}`);
            const okButton = document.querySelector(okButtonSelector);
            
            if (!okButton) {
                throw new Error(`OK button not found: ${okButtonSelector}`);
            }
            
            if (!this.isElementVisible(okButton)) {
                throw new Error('OK button not visible');
            }
            
            // Scroll to button if needed
            this.scrollElementIntoView(okButton);
            await this.delay(200);
            
            // Click the OK button
            console.log('🖱️ Clicking OK button to dismiss popup...');
            okButton.click();
            console.log('✅ OK button clicked successfully');
            
            // Wait for click to process
            await this.delay(waitAfterClick);
            
            // Verify popup is dismissed
            await this.delay(500);
            const popupStillVisible = document.querySelector(popupSelector);
            if (popupStillVisible && this.isElementVisible(popupStillVisible)) {
                console.warn('⚠️ Popup still visible after clicking OK');
            } else {
                console.log('✅ Popup successfully dismissed');
            }
            
        } catch (error) {
            console.error('❌ Error in popup detection and click:', error);
            throw error;
        }
    }

    async executePreventRedirectEvent(event) {
        console.log('🚫 Starting redirect prevention after login');
        
        const timeout = event.timeout || 5000;
        const blockMethods = event.blockMethods || ["location.href", "location.assign", "location.replace", "meta_refresh"];
        const allowManualNavigation = event.allowManualNavigation !== false;
        
        console.log(`🔒 Preventing redirects for ${timeout}ms using methods:`, blockMethods);
        
        // Store original functions to restore later
        const originalFunctions = {};
        const preventionActive = { active: true };
        
        try {
            // Method 1: Block window.location changes
            if (blockMethods.includes("location.href")) {
                console.log('🚫 Blocking window.location.href changes');
                
                // Store original descriptor
                const originalDescriptor = Object.getOwnPropertyDescriptor(window.location, 'href') || 
                                          Object.getOwnPropertyDescriptor(Location.prototype, 'href');
                
                // Override location.href setter
                Object.defineProperty(window.location, 'href', {
                    get: function() {
                        return originalDescriptor ? originalDescriptor.get.call(this) : window.location.href;
                    },
                    set: function(value) {
                        if (preventionActive.active) {
                            console.log(`🚫 Blocked redirect attempt via location.href to: ${value}`);
                            return false;
                        }
                        return originalDescriptor ? originalDescriptor.set.call(this, value) : value;
                    },
                    configurable: true
                });
            }
            
            // Method 2: Block location.assign()
            if (blockMethods.includes("location.assign")) {
                console.log('🚫 Blocking window.location.assign()');
                originalFunctions.assign = window.location.assign;
                window.location.assign = function(url) {
                    if (preventionActive.active) {
                        console.log(`🚫 Blocked redirect attempt via location.assign() to: ${url}`);
                        return false;
                    }
                    return originalFunctions.assign.call(this, url);
                };
            }
            
            // Method 3: Block location.replace()
            if (blockMethods.includes("location.replace")) {
                console.log('🚫 Blocking window.location.replace()');
                originalFunctions.replace = window.location.replace;
                window.location.replace = function(url) {
                    if (preventionActive.active) {
                        console.log(`🚫 Blocked redirect attempt via location.replace() to: ${url}`);
                        return false;
                    }
                    return originalFunctions.replace.call(this, url);
                };
            }
            
            // Method 4: Block meta refresh redirects
            if (blockMethods.includes("meta_refresh")) {
                console.log('🚫 Monitoring and blocking meta refresh redirects');
                
                // Check existing meta refresh tags
                const existingMetaTags = document.querySelectorAll('meta[http-equiv="refresh"]');
                existingMetaTags.forEach(meta => {
                    if (preventionActive.active) {
                        console.log(`🚫 Disabled existing meta refresh tag: ${meta.content}`);
                        meta.remove();
                    }
                });
                
                // Monitor for new meta refresh tags
                const metaObserver = new MutationObserver(mutations => {
                    mutations.forEach(mutation => {
                        if (preventionActive.active) {
                            mutation.addedNodes.forEach(node => {
                                if (node.nodeType === 1) { // Element node
                                    const metaTags = node.querySelectorAll ? 
                                        node.querySelectorAll('meta[http-equiv="refresh"]') : 
                                        (node.matches && node.matches('meta[http-equiv="refresh"]') ? [node] : []);
                                    
                                    metaTags.forEach(meta => {
                                        console.log(`🚫 Blocked new meta refresh tag: ${meta.content}`);
                                        meta.remove();
                                    });
                                }
                            });
                        }
                    });
                });
                
                metaObserver.observe(document, {
                    childList: true,
                    subtree: true
                });
                
                // Store observer for cleanup
                originalFunctions.metaObserver = metaObserver;
            }
            
            // Method 5: Block programmatic navigation
            console.log('🚫 Blocking history manipulation');
            originalFunctions.pushState = window.history.pushState;
            originalFunctions.replaceState = window.history.replaceState;
            
            window.history.pushState = function(state, title, url) {
                if (preventionActive.active && url && url !== window.location.href) {
                    console.log(`🚫 Blocked history.pushState() to: ${url}`);
                    return false;
                }
                return originalFunctions.pushState.call(this, state, title, url);
            };
            
            window.history.replaceState = function(state, title, url) {
                if (preventionActive.active && url && url !== window.location.href) {
                    console.log(`🚫 Blocked history.replaceState() to: ${url}`);
                    return false;
                }
                return originalFunctions.replaceState.call(this, state, title, url);
            };
            
            // Method 6: Monitor and block window.open redirects to same window
            console.log('🚫 Monitoring window.open for same-window redirects');
            originalFunctions.open = window.open;
            window.open = function(url, target, features) {
                if (preventionActive.active && (!target || target === '_self' || target === window.name)) {
                    console.log(`🚫 Blocked window.open() same-window redirect to: ${url}`);
                    return null;
                }
                return originalFunctions.open.call(this, url, target, features);
            };
            
            // Method 7: Stop any ongoing page loads
            console.log('⏹️ Stopping any ongoing page loads');
            window.stop();
            
            // Method 8: Prevent beforeunload if specified
            if (!allowManualNavigation) {
                console.log('🚫 Preventing all navigation including manual');
                originalFunctions.beforeunload = window.onbeforeunload;
                window.onbeforeunload = function(e) {
                    if (preventionActive.active) {
                        e.preventDefault();
                        e.returnValue = '';
                        console.log('🚫 Blocked navigation attempt via beforeunload');
                        return '🚫 Navigation blocked by automation';
                    }
                    return originalFunctions.beforeunload ? originalFunctions.beforeunload.call(this, e) : null;
                };
            }
            
            console.log(`✅ Redirect prevention active for ${timeout}ms`);
            console.log(`📍 Current URL: ${window.location.href}`);
            
            // Wait for the specified timeout
            await this.delay(timeout);
            
            console.log('⏰ Redirect prevention timeout reached, restoring normal navigation');
            
        } catch (error) {
            console.error('❌ Error setting up redirect prevention:', error);
            throw error;
        } finally {
            // Restore original functions
            preventionActive.active = false;
            
            try {
                // Restore location methods
                if (originalFunctions.assign) {
                    window.location.assign = originalFunctions.assign;
                }
                if (originalFunctions.replace) {
                    window.location.replace = originalFunctions.replace;
                }
                
                // Restore history methods
                if (originalFunctions.pushState) {
                    window.history.pushState = originalFunctions.pushState;
                }
                if (originalFunctions.replaceState) {
                    window.history.replaceState = originalFunctions.replaceState;
                }
                
                // Restore window.open
                if (originalFunctions.open) {
                    window.open = originalFunctions.open;
                }
                
                // Restore beforeunload
                if (originalFunctions.beforeunload !== undefined) {
                    window.onbeforeunload = originalFunctions.beforeunload;
                }
                
                // Stop meta tag observer
                if (originalFunctions.metaObserver) {
                    originalFunctions.metaObserver.disconnect();
                }
                
                // Restore location.href descriptor (more complex, optional)
                try {
                    delete window.location.href;
                } catch (descriptorError) {
                    console.warn('Could not fully restore location.href descriptor:', descriptorError.message);
                }
                
                console.log('✅ All navigation methods restored');
                console.log(`📍 Final URL: ${window.location.href}`);
                
            } catch (restoreError) {
                console.warn('⚠️ Error restoring some navigation methods:', restoreError.message);
            }
        }
    }
}

// Initialize the Venus-Millware AutoFill content script
const automationBotContent = new AutomationBotContent();