/**
 * Auto Form Fill Pro - Popup Interface Logic
 * Handles user interactions and UI state management
 */

class PopupInterface {
  constructor() {
    this.isEnabled = false;
    this.currentTab = null;
    this.extensionStatus = null;
    this.apiStatus = null;
    this.flowManager = null;
    
    this.init();
  }

  /**
   * Initialize the popup interface
   */
  async init() {
    try {
      // Initialize FlowManager first
      await this.initializeFlowManager();
      
      // Get current tab
      await this.getCurrentTab();
      
      // Set up event listeners
      this.setupEventListeners();
      
      // Load initial data
      await this.loadInitialData();
      
      // Update UI
      this.updateUI();
      
    } catch (error) {
      console.error('Failed to initialize popup:', error);
      this.showToast('Failed to initialize extension', 'error');
    }
  }

  /**
   * Initialize FlowManager
   */
  async initializeFlowManager() {
    try {
      // Load FlowManager script if not already loaded
      if (!window.FlowManager) {
        await this.loadScript('utils/flow-manager.js');
      }
      
      this.flowManager = new FlowManager();
      console.log('✅ FlowManager initialized');
      
      // Load current flow if exists
      await this.loadCurrentFlow();
      
    } catch (error) {
      console.error('❌ Failed to initialize FlowManager:', error);
      this.showToast('Flow management features may not work', 'warning');
    }
  }

  /**
   * Load script dynamically
   */
  loadScript(src) {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = src;
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
  }

  /**
   * Load current flow from storage
   */
  async loadCurrentFlow() {
    try {
      const result = await chrome.storage.local.get(['currentFlow']);
      if (result.currentFlow && this.flowManager) {
        this.flowManager.currentFlow = result.currentFlow;
        await this.flowManager.updateFlowUI();
      }
    } catch (error) {
      console.error('Failed to load current flow:', error);
    }
  }

  /**
   * Get current active tab
   */
  async getCurrentTab() {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    this.currentTab = tab;
  }

  /**
   * Set up all event listeners
   */
  setupEventListeners() {
    // Extension toggle
    const extensionToggle = document.getElementById('extensionToggle');
    extensionToggle?.addEventListener('change', this.handleExtensionToggle.bind(this));

    // Action buttons
    const runAutomationBtn = document.getElementById('runAutomationBtn');
    runAutomationBtn?.addEventListener('click', this.handleRunAutomation.bind(this));

    const testConnectionBtn = document.getElementById('testConnectionBtn');
    testConnectionBtn?.addEventListener('click', this.handleTestConnection.bind(this));

    const detectFormsBtn = document.getElementById('detectFormsBtn');
    detectFormsBtn?.addEventListener('click', this.handleDetectForms.bind(this));

    // Page info refresh
    const refreshPageInfo = document.getElementById('refreshPageInfo');
    refreshPageInfo?.addEventListener('click', this.handleRefreshPageInfo.bind(this));

    // API test
    const testApiBtn = document.getElementById('testApiBtn');
    testApiBtn?.addEventListener('click', this.handleTestApi.bind(this));

    // Configuration button
    const configBtn = document.getElementById('configBtn');
    configBtn?.addEventListener('click', this.handleOpenConfig.bind(this));

    // Quick settings
    this.setupQuickSettings();

    // Footer links
    const openOptions = document.getElementById('openOptions');
    openOptions?.addEventListener('click', this.handleOpenOptions.bind(this));

    const showHelp = document.getElementById('showHelp');
    showHelp?.addEventListener('click', this.handleShowHelp.bind(this));

    const showLogs = document.getElementById('showLogs');
    showLogs?.addEventListener('click', this.handleShowLogs.bind(this));

    // Settings button
    const settingsBtn = document.getElementById('settingsBtn');
    settingsBtn?.addEventListener('click', this.handleOpenOptions.bind(this));

    // Enhanced Flow Management Event Listeners
    this.setupEnhancedFlowManagement();

    // Set up flow event listeners for individual testing
    this.setupFlowEventListeners();

    // Test buttons
    const testAllEventsBtn = document.getElementById('testAllEventsBtn');
    if (testAllEventsBtn) {
      testAllEventsBtn.addEventListener('click', () => this.handleTestAllEvents());
    }

    // Popup testing buttons
    const testPopupBtn = document.getElementById('testPopupBtn');
    if (testPopupBtn) {
      testPopupBtn.addEventListener('click', () => this.handleTestPopupDetection());
    }

    const testStabilityBtn = document.getElementById('testStabilityBtn');
    if (testStabilityBtn) {
      testStabilityBtn.addEventListener('click', () => this.handleTestPageStability());
    }

    const testOkButtonBtn = document.getElementById('testOkButtonBtn');
    if (testOkButtonBtn) {
      testOkButtonBtn.addEventListener('click', () => this.handleTestPopupOkClick());
    }
  }

  /**
   * Set up quick settings event listeners
   */
  setupQuickSettings() {
    const settings = [
      'highlightFields',
      'confirmBeforeFill',
      'showNotifications',
      'smartDetection'
    ];

    settings.forEach(settingId => {
      const element = document.getElementById(settingId);
      element?.addEventListener('change', (e) => {
        this.handleQuickSettingChange(settingId, e.target.checked);
      });
    });
  }

  /**
   * Set up enhanced flow management event listeners
   */
  setupEnhancedFlowManagement() {
    // File Operations
    const saveFlowToFile = document.getElementById('saveFlowToFile');
    saveFlowToFile?.addEventListener('click', this.handleSaveFlowToFile.bind(this));

    const loadFlowFromFile = document.getElementById('loadFlowFromFile');
    loadFlowFromFile?.addEventListener('click', this.handleLoadFlowFromFile.bind(this));

    const deleteFlowFile = document.getElementById('deleteFlowFile');
    deleteFlowFile?.addEventListener('click', this.handleDeleteFlowFile.bind(this));

    // Validation Controls
    const validateFlow = document.getElementById('validateFlow');
    validateFlow?.addEventListener('click', this.handleValidateFlow.bind(this));

    const dryRunFlow = document.getElementById('dryRunFlow');
    dryRunFlow?.addEventListener('click', this.handleDryRunFlow.bind(this));

    const preflightCheck = document.getElementById('preflightCheck');
    preflightCheck?.addEventListener('click', this.handlePreflightCheck.bind(this));

    // Enhanced Flow Event Listeners
    this.setupFlowEventListeners();
  }

  /**
   * Set up flow event listeners for individual testing
   */
  setupFlowEventListeners() {
    document.addEventListener('click', (e) => {
      // Test Single Event
      if (e.target.classList.contains('test-single-event')) {
        const eventIndex = parseInt(e.target.dataset.eventIndex);
        this.handleTestSingleEvent(eventIndex);
      }

      // Execute Single Event
      if (e.target.classList.contains('execute-single-event')) {
        const eventIndex = parseInt(e.target.dataset.eventIndex);
        this.handleExecuteSingleEvent(eventIndex);
      }

      // Test All Events
      if (e.target.classList.contains('test-all-events')) {
        this.handleTestAllEvents();
      }

      // Edit Event
      if (e.target.classList.contains('edit-event')) {
        const eventIndex = parseInt(e.target.dataset.eventIndex);
        this.handleEditEvent(eventIndex);
      }

      // Delete Event
      if (e.target.classList.contains('delete-event')) {
        const eventIndex = parseInt(e.target.dataset.eventIndex);
        this.handleDeleteEvent(eventIndex);
      }

      // Add Event
      if (e.target.classList.contains('add-event')) {
        this.handleAddEvent();
      }
    });
  }

  /**
   * Load initial data from background script
   */
  async loadInitialData() {
    try {
      // Get extension status
      const statusResponse = await this.sendMessage({ action: 'GET_EXTENSION_STATUS' });
      if (statusResponse.success) {
        this.extensionStatus = statusResponse.data;
        this.isEnabled = statusResponse.data.isEnabled;
      }

      // Get configuration
      const configResponse = await this.sendMessage({ action: 'GET_CONFIGURATION' });
      if (configResponse.success) {
        this.configuration = configResponse.data;
      }

      // Test API connection
      await this.testApiConnection();

      // Get page information
      await this.refreshPageInfo();

    } catch (error) {
      console.error('Failed to load initial data:', error);
    }
  }

  /**
   * Handle extension toggle
   */
  async handleExtensionToggle(event) {
    const isEnabled = event.target.checked;
    
    try {
      this.showLoading('Updating extension status...');
      
      const response = await this.sendMessage({ action: 'TOGGLE_EXTENSION' });
      
      if (response.success) {
        this.isEnabled = response.data.isEnabled;
        this.updateToggleState();
        this.showToast(
          `Extension ${this.isEnabled ? 'enabled' : 'disabled'}`,
          'success'
        );
      } else {
        throw new Error(response.error || 'Failed to toggle extension');
      }
    } catch (error) {
      console.error('Toggle error:', error);
      this.showToast('Failed to toggle extension', 'error');
      // Revert toggle state
      event.target.checked = this.isEnabled;
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Handle run automation action
   */
  async handleRunAutomation(event) {
    if (!this.isEnabled) {
      this.showToast('Extension is disabled', 'warning');
      return;
    }

    try {
      const button = event.target.closest('.action-btn');
      this.setButtonLoading(button, true);

      this.showLoading('Starting automation flow...');

      // Step 1: Test API connection first
      this.updateLoadingText('Testing API connection...');
      const apiTest = await this.sendMessage({ action: 'TEST_API_CONNECTION' });

      if (!apiTest.success) {
        throw new Error(`API connection failed: ${apiTest.error}`);
      }

      // Step 2: Fetch staging data
      this.updateLoadingText('Fetching staging data...');
      const dataResponse = await this.sendMessage({ action: 'FETCH_STAGING_DATA' });

      if (!dataResponse.success) {
        throw new Error(`Failed to fetch staging data: ${dataResponse.error}`);
      }

      // Step 3: Navigate to target website and run automation
      this.updateLoadingText('Starting login automation...');
      const automationResponse = await this.sendMessage({
        action: 'RUN_COMPLETE_AUTOMATION',
        data: {
          tabId: this.currentTab.id,
          stagingData: dataResponse.data,
          targetUrl: 'http://millwarep3.rebinmas.com:8003/',
          credentials: {
            username: 'adm075',
            password: 'adm075'
          }
        }
      });

      if (automationResponse.success) {
        this.showToast('Automation completed successfully!', 'success');
        await this.refreshPageInfo();
      } else {
        throw new Error(automationResponse.error || 'Automation failed');
      }
    } catch (error) {
      console.error('Automation error:', error);
      this.showToast(`Automation failed: ${error.message}`, 'error');
    } finally {
      const button = event.target.closest('.action-btn');
      this.setButtonLoading(button, false);
      this.hideLoading();
    }
  }

  /**
   * Handle test connection action
   */
  async handleTestConnection(event) {
    try {
      const button = event.target.closest('.action-btn');
      this.setButtonLoading(button, true);

      const response = await this.sendMessage({ action: 'TEST_API_CONNECTION' });

      if (response.success) {
        this.showToast('API connection successful!', 'success');
        this.updateApiStatus(response.data);
      } else {
        throw new Error(response.error || 'Connection test failed');
      }
    } catch (error) {
      console.error('Connection test error:', error);
      this.showToast(`Connection test failed: ${error.message}`, 'error');
    } finally {
      const button = event.target.closest('.action-btn');
      this.setButtonLoading(button, false);
    }
  }

  /**
   * Handle open configuration
   */
  handleOpenConfig() {
    this.showToast('Configuration panel coming soon', 'info');
  }

  /**
   * Handle detect forms action
   */
  async handleDetectForms(event) {
    try {
      const button = event.target.closest('.action-btn');
      this.setButtonLoading(button, true);

      // Send message to content script to detect forms
      const response = await chrome.tabs.sendMessage(this.currentTab.id, {
        action: 'DETECT_FORMS'
      });

      if (response?.success) {
        const { forms, fields } = response.data;
        this.updatePageInfo({
          formsCount: forms || 0,
          fieldsCount: fields || 0
        });
        this.showToast(`Found ${forms} forms with ${fields} fields`, 'success');
      } else {
        this.showToast('No forms detected on this page', 'warning');
      }
    } catch (error) {
      console.error('Detect forms error:', error);
      this.showToast('Failed to detect forms', 'error');
    } finally {
      const button = event.target.closest('.action-btn');
      this.setButtonLoading(button, false);
    }
  }

  /**
   * Handle refresh page info
   */
  async handleRefreshPageInfo() {
    try {
      await this.refreshPageInfo();
      this.showToast('Page information refreshed', 'success');
    } catch (error) {
      console.error('Refresh error:', error);
      this.showToast('Failed to refresh page info', 'error');
    }
  }

  /**
   * Handle API test
   */
  async handleTestApi() {
    try {
      await this.testApiConnection();
      this.showToast('API connection tested', 'success');
    } catch (error) {
      console.error('API test error:', error);
      this.showToast('API test failed', 'error');
    }
  }

  /**
   * Handle quick setting changes
   */
  async handleQuickSettingChange(settingId, value) {
    try {
      const settingMapping = {
        highlightFields: 'autoFill.highlightFields',
        confirmBeforeFill: 'autoFill.confirmBeforeFill',
        showNotifications: 'ui.showNotifications',
        smartDetection: 'autoFill.smartFieldDetection'
      };

      const configPath = settingMapping[settingId];
      if (!configPath) return;

      const [section, key] = configPath.split('.');
      const updates = { [section]: { [key]: value } };

      const response = await this.sendMessage({
        action: 'UPDATE_CONFIGURATION',
        data: updates
      });

      if (response.success) {
        this.showToast('Setting updated', 'success');
      } else {
        throw new Error(response.error || 'Failed to update setting');
      }
    } catch (error) {
      console.error('Setting update error:', error);
      this.showToast('Failed to update setting', 'error');
    }
  }

  /**
   * Handle open options page
   */
  handleOpenOptions() {
    chrome.runtime.openOptionsPage();
  }

  /**
   * Handle show help
   */
  handleShowHelp() {
    const helpUrl = chrome.runtime.getURL('help/help.html');
    chrome.tabs.create({ url: helpUrl });
  }

  /**
   * Handle show logs
   */
  async handleShowLogs() {
    try {
      // For now, just show a simple logs dialog
      // In a full implementation, you might open a dedicated logs page
      this.showToast('Logs feature coming soon', 'info');
    } catch (error) {
      console.error('Show logs error:', error);
    }
  }

  /**
   * Test API connection
   */
  async testApiConnection() {
    try {
      const response = await this.sendMessage({ action: 'TEST_API_CONNECTION' });
      
      if (response.success) {
        this.apiStatus = response.data;
        this.updateApiStatus();
      } else {
        this.apiStatus = { success: false, error: response.error };
        this.updateApiStatus();
      }
    } catch (error) {
      this.apiStatus = { success: false, error: error.message };
      this.updateApiStatus();
    }
  }

  /**
   * Refresh page information
   */
  async refreshPageInfo() {
    try {
      if (!this.currentTab) return;

      // Update domain info
      const domain = new URL(this.currentTab.url).hostname;
      this.updatePageInfo({ domain });

      // Try to get form information from content script
      try {
        const response = await chrome.tabs.sendMessage(this.currentTab.id, {
          action: 'GET_PAGE_STATS'
        });

        if (response?.success) {
          this.updatePageInfo(response.data);
        }
      } catch (error) {
        // Content script might not be injected yet
        this.updatePageInfo({ formsCount: 0, fieldsCount: 0 });
      }
    } catch (error) {
      console.error('Refresh page info error:', error);
    }
  }

  /**
   * Update UI components
   */
  updateUI() {
    this.updateToggleState();
    this.updateStatusIndicator();
    this.updateActionButtons();
    this.updateQuickSettings();
  }

  /**
   * Update toggle state
   */
  updateToggleState() {
    const toggle = document.getElementById('extensionToggle');
    const description = document.getElementById('toggleDescription');
    
    if (toggle) {
      toggle.checked = this.isEnabled;
    }
    
    if (description) {
      description.textContent = this.isEnabled 
        ? 'Extension is active and ready to fill forms'
        : 'Extension is disabled';
    }
  }

  /**
   * Update status indicator
   */
  updateStatusIndicator() {
    const statusDot = document.getElementById('statusDot');
    const statusText = document.getElementById('statusText');
    
    if (!statusDot || !statusText) return;

    if (this.isEnabled && this.apiStatus?.success) {
      statusDot.className = 'status-dot connected';
      statusText.textContent = 'Ready';
    } else if (!this.isEnabled) {
      statusDot.className = 'status-dot';
      statusText.textContent = 'Disabled';
    } else {
      statusDot.className = 'status-dot error';
      statusText.textContent = 'API Error';
    }
  }

  /**
   * Update action buttons
   */
  updateActionButtons() {
    const runBtn = document.getElementById('runAutomationBtn');
    const testBtn = document.getElementById('testConnectionBtn');

    if (runBtn) {
      runBtn.disabled = !this.isEnabled;
    }

    if (testBtn) {
      testBtn.disabled = false; // Always allow testing connection
    }
  }

  /**
   * Update quick settings
   */
  updateQuickSettings() {
    if (!this.configuration) return;

    const settings = {
      highlightFields: this.configuration.autoFill?.highlightFields,
      confirmBeforeFill: this.configuration.autoFill?.confirmBeforeFill,
      showNotifications: this.configuration.ui?.showNotifications,
      smartDetection: this.configuration.autoFill?.smartFieldDetection
    };

    Object.entries(settings).forEach(([id, value]) => {
      const element = document.getElementById(id);
      if (element && typeof value === 'boolean') {
        element.checked = value;
      }
    });
  }

  /**
   * Update page information display
   */
  updatePageInfo(info) {
    const elements = {
      currentDomain: info.domain,
      formsCount: info.formsCount,
      fieldsCount: info.fieldsCount
    };

    Object.entries(elements).forEach(([id, value]) => {
      const element = document.getElementById(id);
      if (element && value !== undefined) {
        element.textContent = value;
      }
    });
  }

  /**
   * Update API status display
   */
  updateApiStatus(statusData = null) {
    const data = statusData || this.apiStatus;

    const elements = {
      apiConnection: document.getElementById('apiConnection'),
      apiAuth: document.getElementById('apiAuth'),
      apiLastResponse: document.getElementById('apiLastResponse'),
      apiEndpoint: document.getElementById('apiEndpoint')
    };

    if (elements.apiConnection) {
      if (data?.success || data?.status === 'connected') {
        elements.apiConnection.textContent = 'Connected';
        elements.apiConnection.className = 'detail-value success';
      } else {
        elements.apiConnection.textContent = 'Failed';
        elements.apiConnection.className = 'detail-value error';
      }
    }

    if (elements.apiAuth) {
      const isConnected = data?.success || data?.status === 'connected';
      elements.apiAuth.textContent = isConnected ? 'Valid' : 'Invalid';
      elements.apiAuth.className = isConnected ? 'detail-value success' : 'detail-value error';
    }

    if (elements.apiLastResponse) {
      const timestamp = data?.timestamp;
      if (timestamp) {
        const time = new Date(timestamp).toLocaleTimeString();
        elements.apiLastResponse.textContent = time;
      }
    }

    // Update API endpoint display with connection details
    if (elements.apiEndpoint) {
      if (data.success) {
        elements.apiEndpoint.textContent = `localhost:5173 (${data.recordCount} records)`;
        elements.apiEndpoint.className = 'status connected';
      } else {
        elements.apiEndpoint.textContent = 'localhost:5173 (offline)';
        elements.apiEndpoint.className = 'status error';
      }
    }
  }

  /**
   * Set button loading state
   */
  setButtonLoading(button, isLoading) {
    if (!button) return;

    if (isLoading) {
      button.classList.add('loading');
      button.disabled = true;
    } else {
      button.classList.remove('loading');
      button.disabled = false;
    }
  }

  /**
   * Show loading overlay
   */
  showLoading(text = 'Loading...') {
    const overlay = document.getElementById('loadingOverlay');
    const loadingText = document.getElementById('loadingText');
    
    if (overlay) {
      overlay.classList.add('visible');
    }
    
    if (loadingText) {
      loadingText.textContent = text;
    }
  }

  /**
   * Hide loading overlay
   */
  hideLoading() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
      overlay.classList.remove('visible');
    }
  }

  /**
   * Update loading text
   */
  updateLoadingText(text) {
    const loadingText = document.getElementById('loadingText');
    if (loadingText) {
      loadingText.textContent = text;
    }
  }

  /**
   * Show toast notification
   */
  showToast(message, type = 'info', duration = 3000) {
    const container = document.getElementById('toastContainer');
    if (!container) return;

    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    
    toast.innerHTML = `
      <div class="toast-content">
        <div class="toast-text">${message}</div>
        <button class="toast-close">&times;</button>
      </div>
    `;

    // Add close functionality
    const closeBtn = toast.querySelector('.toast-close');
    closeBtn.addEventListener('click', () => {
      this.removeToast(toast);
    });

    container.appendChild(toast);

    // Show toast
    setTimeout(() => {
      toast.classList.add('visible');
    }, 10);

    // Auto-remove after duration
    setTimeout(() => {
      this.removeToast(toast);
    }, duration);
  }

  /**
   * Remove toast notification
   */
  removeToast(toast) {
    toast.classList.remove('visible');
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    }, 300);
  }

  /**
   * Send message to background script
   */
  async sendMessage(message) {
    return new Promise((resolve) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          resolve({ success: false, error: chrome.runtime.lastError.message });
        } else {
          resolve(response || { success: false, error: 'No response' });
        }
      });
    });
  }

  // ================== ENHANCED FLOW MANAGEMENT HANDLERS ==================

  /**
   * Handle save flow to file
   */
  async handleSaveFlowToFile() {
    try {
      if (!this.flowManager) {
        throw new Error('Flow Manager not initialized');
      }

      this.showLoading('Saving flow to primary source...');
      await this.flowManager.saveFlowToPrimarySource();
      
    } catch (error) {
      console.error('❌ Error saving flow to file:', error);
      this.showToast(`Failed to save flow: ${error.message}`, 'error');
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Handle load flow from file
   */
  async handleLoadFlowFromFile() {
    try {
      if (!this.flowManager) {
        throw new Error('Flow Manager not initialized');
      }

      this.showLoading('Loading flow from primary source...');
      await this.flowManager.loadFlowFromPrimarySource();
      
      // Update flow UI after loading
      await this.updateFlowDisplays();
      
    } catch (error) {
      console.error('❌ Error loading flow from file:', error);
      this.showToast(`Failed to load flow: ${error.message}`, 'error');
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Handle delete flow file
   */
  async handleDeleteFlowFile() {
    try {
      if (!this.flowManager) {
        throw new Error('Flow Manager not initialized');
      }

      this.showLoading('Deleting flow...');
      await this.flowManager.deleteFlowFile();
      
      // Update flow UI after deletion
      await this.updateFlowDisplays();
      
    } catch (error) {
      console.error('❌ Error deleting flow:', error);
      this.showToast(`Failed to delete flow: ${error.message}`, 'error');
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Handle validate flow
   */
  async handleValidateFlow() {
    try {
      if (!this.flowManager) {
        throw new Error('Flow Manager not initialized');
      }

      this.showLoading('Validating flow...');
      const validation = await this.flowManager.validateCurrentFlow();
      
      // Update validation status in UI
      this.updateValidationStatus(validation);
      
    } catch (error) {
      console.error('❌ Error validating flow:', error);
      this.showToast(`Validation failed: ${error.message}`, 'error');
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Handle dry run flow
   */
  async handleDryRunFlow() {
    try {
      if (!this.flowManager) {
        throw new Error('Flow Manager not initialized');
      }

      this.showLoading('Performing dry run...');
      await this.flowManager.performDryRun();
      
    } catch (error) {
      console.error('❌ Error performing dry run:', error);
      this.showToast(`Dry run failed: ${error.message}`, 'error');
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Handle preflight check
   */
  async handlePreflightCheck() {
    try {
      if (!this.flowManager || !this.flowManager.currentFlow) {
        throw new Error('No flow available for preflight check');
      }

      this.showLoading('Performing preflight check...');
      
      const preflightResult = await this.flowManager.flowValidator.preflightCheck(
        this.flowManager.currentFlow
      );
      
      if (preflightResult) {
        const message = `Preflight Check: ${preflightResult.elementsFound}/${preflightResult.totalElements} elements found`;
        const type = preflightResult.elementsFound === preflightResult.totalElements ? 'success' : 'warning';
        this.showToast(message, type);
        
        if (preflightResult.missingElements.length > 0) {
          console.warn('Missing elements:', preflightResult.missingElements);
        }
      }
      
    } catch (error) {
      console.error('❌ Error performing preflight check:', error);
      this.showToast(`Preflight check failed: ${error.message}`, 'error');
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Handle test single event
   */
  async handleTestSingleEvent(eventIndex) {
    try {
      if (!this.flowManager || !this.flowManager.currentFlow) {
        throw new Error('No flow loaded');
      }

      const flow = this.flowManager.currentFlow;
      if (!flow.flow_steps || eventIndex >= flow.flow_steps.length) {
        throw new Error('Invalid event index');
      }

      const event = flow.flow_steps[eventIndex];
      this.showLoading(`Testing event: ${event.name || `Event ${eventIndex + 1}`}`);

      // Update event status to testing
      this.updateEventStatus(eventIndex, 'testing');

      // Get active tab and send test message
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      if (!tab) {
        throw new Error('No active tab found');
      }

      // Send test message to content script
      const response = await chrome.tabs.sendMessage(tab.id, {
        action: 'testEvent',
        event: event,
        index: eventIndex
      });

      if (response.success) {
        this.updateEventStatus(eventIndex, 'success');
        this.showToast(`✅ Event test passed: ${event.name}`, 'success');
        
        // Show detailed test results
        this.showEventTestResults(eventIndex, response.testResult);
      } else {
        this.updateEventStatus(eventIndex, 'error');
        this.showToast(`❌ Event test failed: ${response.error}`, 'error');
        
        // Show error details
        this.showEventTestResults(eventIndex, response.testResult);
      }

    } catch (error) {
      console.error('❌ Test single event failed:', error);
      this.updateEventStatus(eventIndex, 'error');
      this.showToast(`Failed to test event: ${error.message}`, 'error');
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Handle execute single event
   */
  async handleExecuteSingleEvent(eventIndex) {
    try {
      if (!this.flowManager || !this.flowManager.currentFlow) {
        throw new Error('No flow loaded');
      }

      const flow = this.flowManager.currentFlow;
      if (!flow.flow_steps || eventIndex >= flow.flow_steps.length) {
        throw new Error('Invalid event index');
      }

      const event = flow.flow_steps[eventIndex];
      this.showLoading(`Executing event: ${event.name || `Event ${eventIndex + 1}`}`);

      // Update event status to executing
      this.updateEventStatus(eventIndex, 'executing');

      // Get active tab and send execute message
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      if (!tab) {
        throw new Error('No active tab found');
      }

      // Send execute message to content script
      const response = await chrome.tabs.sendMessage(tab.id, {
        action: 'executeSingleEvent',
        event: event,
        index: eventIndex
      });

      if (response.success) {
        this.updateEventStatus(eventIndex, 'success');
        this.showToast(`✅ Event executed successfully: ${event.name}`, 'success');
      } else {
        this.updateEventStatus(eventIndex, 'error');
        this.showToast(`❌ Event execution failed: ${response.error}`, 'error');
      }

    } catch (error) {
      console.error('❌ Execute single event failed:', error);
      this.updateEventStatus(eventIndex, 'error');
      this.showToast(`Failed to execute event: ${error.message}`, 'error');
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Show element manager interface
   */
  async handleShowElementManager(eventIndex) {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      if (!tab) {
        throw new Error('No active tab found');
      }

      // Show element manager on the page
      const response = await chrome.tabs.sendMessage(tab.id, {
        action: 'showElementManager',
        eventIndex: eventIndex
      });

      if (response && response.success) {
        this.showToast('Element manager opened on page', 'success');
      } else {
        throw new Error(response?.error || 'Failed to show element manager');
      }

    } catch (error) {
      console.error('❌ Show element manager failed:', error);
      this.showToast(`Failed to show element manager: ${error.message}`, 'error');
    }
  }

  /**
   * Handle highlight element
   */
  async handleHighlightElement(eventIndex) {
    try {
      if (!this.flowManager || !this.flowManager.currentFlow) {
        throw new Error('No flow loaded');
      }

      const flow = this.flowManager.currentFlow;
      if (!flow.flow_steps || eventIndex >= flow.flow_steps.length) {
        throw new Error('Invalid event index');
      }

      const event = flow.flow_steps[eventIndex];
      
      // Extract targeting configuration from event
      const targetConfig = this.extractTargetConfig(event);
      
      if (!targetConfig) {
        throw new Error('No valid targeting configuration found in event');
      }

      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      if (!tab) {
        throw new Error('No active tab found');
      }

      // Send highlight message to content script
      const response = await chrome.tabs.sendMessage(tab.id, {
        action: 'highlightElement',
        targetConfig: targetConfig,
        highlightOptions: {
          duration: 3000,
          blinkCount: 4,
          highlightColor: '#ff0000'
        }
      });

      if (response && response.success) {
        this.showToast(`✨ Element highlighted using ${response.method}`, 'success');
      } else {
        throw new Error(response?.error || 'Failed to highlight element');
      }

    } catch (error) {
      console.error('❌ Highlight element failed:', error);
      this.showToast(`Failed to highlight element: ${error.message}`, 'error');
    }
  }

  /**
   * Extract targeting configuration from event
   */
  extractTargetConfig(event) {
    const targetConfig = {};

    // Extract selector from event parameters
    if (event.parameters && event.parameters.selector) {
      targetConfig.selector = event.parameters.selector;
    }

    // Extract XPath if available
    if (event.parameters && event.parameters.xpath) {
      targetConfig.xpath = event.parameters.xpath;
    }

    // Extract text-based targeting
    if (event.parameters && event.parameters.searchTexts) {
      targetConfig.text = {
        text: event.parameters.searchTexts[0],
        partial: true,
        caseSensitive: false
      };
    }

    // Extract attribute-based targeting
    if (event.parameters && event.parameters.attribute) {
      targetConfig.attribute = event.parameters.attribute;
    }

    // Add alternatives if available
    if (event.parameters && event.parameters.selector_alternatives) {
      targetConfig.alternatives = event.parameters.selector_alternatives;
    }

    return Object.keys(targetConfig).length > 0 ? targetConfig : null;
  }

  /**
   * Update event status visual indicator
   */
  updateEventStatus(eventIndex, status) {
    const statusElement = document.getElementById(`eventStatus_${eventIndex}`);
    if (!statusElement) return;

    const statusDot = statusElement.querySelector('.status-dot');
    if (!statusDot) return;

    // Remove all status classes
    statusDot.classList.remove('status-pending', 'status-testing', 'status-executing', 'status-success', 'status-error', 'status-warning');

    // Add new status class
    statusDot.classList.add(`status-${status}`);
    statusDot.setAttribute('data-status', status);

    // Update title attribute
    const statusTitles = {
      pending: 'Not tested',
      testing: 'Testing in progress...',
      executing: 'Executing...',
      success: 'Test/Execution successful',
      error: 'Test/Execution failed',
      warning: 'Test/Execution completed with warnings'
    };

    statusDot.setAttribute('title', statusTitles[status] || status);
  }

  /**
   * Show event test results
   */
  showEventTestResults(eventIndex, testResult) {
    if (!testResult) return;

    const modalHTML = `
      <div class="event-test-results">
        <h6>Test Results for Event ${eventIndex + 1}</h6>
        
        <div class="test-summary">
          <div class="test-status ${testResult.success ? 'success' : 'error'}">
            ${testResult.success ? '✅ Test Passed' : '❌ Test Failed'}
          </div>
          <div class="test-duration">Duration: ${testResult.duration || 0}ms</div>
        </div>

        ${testResult.error ? `
          <div class="test-error">
            <h6>Error:</h6>
            <p>${testResult.error}</p>
          </div>
        ` : ''}

        ${testResult.details ? `
          <div class="test-details">
            <h6>Details:</h6>
            <pre>${JSON.stringify(testResult.details, null, 2)}</pre>
          </div>
        ` : ''}

        <div class="modal-buttons">
          <button class="btn btn-secondary close-modal">Close</button>
        </div>
      </div>
    `;

    this.showModal('Event Test Results', modalHTML);
  }

  /**
   * Handle test all events
   */
  async handleTestAllEvents() {
    try {
      if (!this.flowManager) {
        throw new Error('Flow Manager not initialized');
      }

      this.showLoading('Testing all events...');
      await this.flowManager.testAllEvents();
      
    } catch (error) {
      console.error('❌ Error testing all events:', error);
      this.showToast(`Event testing failed: ${error.message}`, 'error');
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Handle test popup detection
   */
  async handleTestPopupDetection() {
    try {
      this.showLoading('Testing popup detection...');

      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      if (!tab) {
        throw new Error('No active tab found');
      }

      // Send popup detection test message
      const response = await chrome.tabs.sendMessage(tab.id, {
        action: 'popup_handler',
        timeout: 5000,
        popupSelectors: [
          '.PopupBoxLogin',
          '[class*="popup"]',
          '[class*="modal"]'
        ],
        okButtonSelectors: [
          '#MainContent_btnOkay',
          '.button',
          'input[type="button"][value*="OK"]'
        ]
      });

      if (response && response.success) {
        const message = response.popupHandled ? 
          '✅ Popup detected and handled successfully' : 
          '⚠️ No popup detected on current page';
        this.showToast(message, response.popupHandled ? 'success' : 'warning');
      } else {
        throw new Error(response?.error || 'Popup detection test failed');
      }

    } catch (error) {
      console.error('❌ Popup detection test failed:', error);
      this.showToast(`Popup detection test failed: ${error.message}`, 'error');
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Handle test page stability
   */
  async handleTestPageStability() {
    try {
      this.showLoading('Testing page stability...');

      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      if (!tab) {
        throw new Error('No active tab found');
      }

      // Send page stability test message
      const response = await chrome.tabs.sendMessage(tab.id, {
        action: 'wait_for_page_stability',
        timeout: 3000,
        maxWait: 10000
      });

      if (response && response.success) {
        this.showToast('✅ Page stability test completed', 'success');
      } else {
        throw new Error(response?.error || 'Page stability test failed');
      }

    } catch (error) {
      console.error('❌ Page stability test failed:', error);
      this.showToast(`Page stability test failed: ${error.message}`, 'error');
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Handle add new event
   */
  async handleAddEvent() {
    try {
      if (!this.flowManager) {
        throw new Error('Flow Manager not initialized');
      }

      // Show add event modal
      this.showAddEventModal();
      
    } catch (error) {
      console.error('❌ Error showing add event modal:', error);
      this.showToast(`Failed to show add event form: ${error.message}`, 'error');
    }
  }

  /**
   * Handle edit event
   */
  async handleEditEvent(eventIndex) {
    try {
      if (!this.flowManager || !this.flowManager.currentFlow) {
        throw new Error('No flow available for editing');
      }

      const event = this.flowManager.currentFlow.flow_steps[eventIndex];
      if (!event) {
        throw new Error(`Event at index ${eventIndex} not found`);
      }

      // Show event edit modal
      this.showEventEditModal(event, eventIndex);
      
    } catch (error) {
      console.error('❌ Error editing event:', error);
      this.showToast(`Failed to edit event: ${error.message}`, 'error');
    }
  }

  /**
   * Handle delete event
   */
  async handleDeleteEvent(eventIndex) {
    try {
      if (!this.flowManager) {
        throw new Error('Flow Manager not initialized');
      }

      this.showLoading('Deleting event...');
      const success = await this.flowManager.deleteEvent(eventIndex);
      
      if (success) {
        await this.updateFlowDisplays();
        this.showToast('✅ Event deleted successfully', 'success');
      }
      
    } catch (error) {
      console.error('❌ Error deleting event:', error);
      this.showToast(`Failed to delete event: ${error.message}`, 'error');
    } finally {
      this.hideLoading();
    }
  }

  // ================== EVENT CRUD MODAL METHODS ==================

  /**
   * Show add event modal
   */
  showAddEventModal() {
    const modalHTML = `
      <div class="event-edit-form">
        <div class="form-group">
          <label for="eventName">Event Name:</label>
          <input type="text" id="eventName" placeholder="Enter event name">
        </div>
        
        <div class="form-group">
          <label for="eventDescription">Description:</label>
          <textarea id="eventDescription" placeholder="Enter event description"></textarea>
        </div>
        
        <div class="form-group">
          <label for="eventType">Event Type:</label>
          <select id="eventType">
            <option value="wait">Wait</option>
            <option value="click">Click</option>
            <option value="input">Input</option>
            <option value="navigate">Navigate</option>
            <option value="text_search_click">Text Search Click</option>
            <option value="conditional_action">Conditional Action</option>
            <option value="extract">Extract</option>
          </select>
        </div>
        
        <div id="eventSpecificFields">
          <!-- Will be populated based on event type -->
        </div>
        
        <div class="form-actions">
          <button class="btn btn-primary" onclick="popupInterface.saveNewEvent()">Add Event</button>
          <button class="btn btn-secondary modal-close">Cancel</button>
        </div>
      </div>
    `;

    this.showModal('Add New Event', modalHTML);

    // Setup event type change handler
    const eventTypeSelect = document.getElementById('eventType');
    eventTypeSelect?.addEventListener('change', (e) => {
      this.updateEventSpecificFields(e.target.value);
    });

    // Initialize with default fields
    this.updateEventSpecificFields('wait');
  }

  /**
   * Save new event
   */
  async saveNewEvent() {
    try {
      const eventData = this.collectEventFormData();
      
      this.showLoading('Adding event...');
      await this.flowManager.addEvent(eventData);
      
      // Close modal
      const modal = document.querySelector('.modal-overlay');
      if (modal) document.body.removeChild(modal);
      
      // Update UI
      await this.updateFlowDisplays();
      
    } catch (error) {
      console.error('❌ Error saving new event:', error);
      this.showToast(`Failed to add event: ${error.message}`, 'error');
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Save event changes
   */
  async saveEventChanges(eventIndex) {
    try {
      const eventData = this.collectEventFormData();
      
      this.showLoading('Updating event...');
      await this.flowManager.updateEvent(eventIndex, eventData);
      
      // Close modal
      const modal = document.querySelector('.modal-overlay');
      if (modal) document.body.removeChild(modal);
      
      // Update UI
      await this.updateFlowDisplays();
      
    } catch (error) {
      console.error('❌ Error saving event changes:', error);
      this.showToast(`Failed to update event: ${error.message}`, 'error');
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Collect event form data
   */
  collectEventFormData() {
    const name = document.getElementById('eventName')?.value || '';
    const description = document.getElementById('eventDescription')?.value || '';
    const type = document.getElementById('eventType')?.value || 'wait';
    
    const eventData = {
      name,
      description,
      type,
      parameters: {}
    };

    // Collect type-specific parameters
    switch (type) {
      case 'wait':
        eventData.parameters.duration = parseInt(document.getElementById('eventDuration')?.value || 1000);
        break;
        
      case 'click':
        eventData.parameters.selector = document.getElementById('eventSelector')?.value || '';
        break;
        
      case 'input':
        eventData.parameters.selector = document.getElementById('eventSelector')?.value || '';
        eventData.parameters.value = document.getElementById('eventValue')?.value || '';
        eventData.parameters.clear_first = document.getElementById('eventClearFirst')?.checked || false;
        break;
        
      case 'navigate':
        eventData.parameters.url = document.getElementById('eventUrl')?.value || '';
        break;
        
      case 'text_search_click':
        const searchText = document.getElementById('eventSearchText')?.value || '';
        eventData.parameters.searchTexts = [searchText];
        eventData.parameters.timeout = parseInt(document.getElementById('eventTimeout')?.value || 1000);
        break;
    }

    return eventData;
  }

  /**
   * Update event-specific form fields
   */
  updateEventSpecificFields(eventType, existingEvent = null) {
    const container = document.getElementById('eventSpecificFields');
    if (!container) return;

    let fieldsHTML = '';

    switch (eventType) {
      case 'wait':
        fieldsHTML = `
          <div class="form-group">
            <label for="eventDuration">Duration (ms):</label>
            <input type="number" id="eventDuration" value="${existingEvent?.parameters?.duration || 1000}" min="100" max="30000">
          </div>
        `;
        break;
        
      case 'click':
        fieldsHTML = `
          <div class="form-group">
            <label for="eventSelector">CSS Selector:</label>
            <input type="text" id="eventSelector" value="${existingEvent?.parameters?.selector || ''}" placeholder="#button-id, .button-class">
          </div>
        `;
        break;
        
      case 'input':
        fieldsHTML = `
          <div class="form-group">
            <label for="eventSelector">CSS Selector:</label>
            <input type="text" id="eventSelector" value="${existingEvent?.parameters?.selector || ''}" placeholder="#input-id, .input-class">
          </div>
          <div class="form-group">
            <label for="eventValue">Input Value:</label>
            <input type="text" id="eventValue" value="${existingEvent?.parameters?.value || ''}" placeholder="Text to input">
          </div>
          <div class="form-group">
            <label class="checkbox-label">
              <input type="checkbox" id="eventClearFirst" ${existingEvent?.parameters?.clear_first ? 'checked' : ''}>
              Clear field before input
            </label>
          </div>
        `;
        break;
        
      case 'navigate':
        fieldsHTML = `
          <div class="form-group">
            <label for="eventUrl">URL:</label>
            <input type="url" id="eventUrl" value="${existingEvent?.parameters?.url || ''}" placeholder="https://example.com">
          </div>
        `;
        break;
        
      case 'text_search_click':
        fieldsHTML = `
          <div class="form-group">
            <label for="eventSearchText">Search Text:</label>
            <input type="text" id="eventSearchText" value="${existingEvent?.parameters?.searchTexts?.[0] || ''}" placeholder="Text to search and click">
          </div>
          <div class="form-group">
            <label for="eventTimeout">Timeout (ms):</label>
            <input type="number" id="eventTimeout" value="${existingEvent?.parameters?.timeout || 1000}" min="100" max="10000">
          </div>
        `;
        break;
        
      default:
        fieldsHTML = '<p>No specific parameters for this event type</p>';
    }

    container.innerHTML = fieldsHTML;
  }

  /**
   * Update validation status display
   */
  updateValidationStatus(validation) {
    const statusElement = document.getElementById('validationStatus');
    if (!statusElement) return;

    let statusClass = 'neutral';
    let statusText = 'No validation performed';

    if (validation) {
      if (validation.isValid) {
        statusClass = 'valid';
        statusText = '✅ Flow is valid';
        if (validation.warnings.length > 0) {
          statusText += ` (${validation.warnings.length} warnings)`;
          statusClass = 'warning';
        }
      } else {
        statusClass = 'invalid';
        statusText = `❌ Flow has ${validation.errors.length} error(s)`;
      }
    }

    statusElement.className = `validation-status ${statusClass}`;
    statusElement.innerHTML = `<span class="status-text">${statusText}</span>`;
  }

  /**
   * Show event edit modal
   */
  showEventEditModal(event, eventIndex) {
    const modalHTML = `
      <div class="event-edit-form">
        <div class="form-group">
          <label for="eventName">Event Name:</label>
          <input type="text" id="eventName" value="${event.name || ''}" placeholder="Enter event name">
        </div>
        
        <div class="form-group">
          <label for="eventDescription">Description:</label>
          <textarea id="eventDescription" placeholder="Enter event description">${event.description || ''}</textarea>
        </div>
        
        <div class="form-group">
          <label for="eventType">Event Type:</label>
          <select id="eventType">
            <option value="wait" ${event.type === 'wait' ? 'selected' : ''}>Wait</option>
            <option value="click" ${event.type === 'click' ? 'selected' : ''}>Click</option>
            <option value="input" ${event.type === 'input' ? 'selected' : ''}>Input</option>
            <option value="navigate" ${event.type === 'navigate' ? 'selected' : ''}>Navigate</option>
            <option value="text_search_click" ${event.type === 'text_search_click' ? 'selected' : ''}>Text Search Click</option>
            <option value="conditional_action" ${event.type === 'conditional_action' ? 'selected' : ''}>Conditional Action</option>
            <option value="extract" ${event.type === 'extract' ? 'selected' : ''}>Extract</option>
          </select>
        </div>
        
        <div id="eventSpecificFields">
          <!-- Will be populated based on event type -->
        </div>
        
        <div class="form-actions">
          <button class="btn btn-primary" onclick="popupInterface.saveEventChanges(${eventIndex})">Save Changes</button>
          <button class="btn btn-secondary modal-close">Cancel</button>
        </div>
      </div>
    `;

    if (this.flowManager && this.flowManager.showModal) {
      this.flowManager.showModal(`Edit Event ${eventIndex + 1}`, modalHTML);
    }

    // Setup event type change handler
    const eventTypeSelect = document.getElementById('eventType');
    eventTypeSelect?.addEventListener('change', (e) => {
      this.updateEventSpecificFields(e.target.value, event);
    });

    // Initialize with current event data
    this.updateEventSpecificFields(event.type, event);
  }

  /**
   * Generate event-specific form fields
   */
  generateEventSpecificFields(event) {
    switch (event.type) {
      case 'click':
        return `
          <div class="form-group">
            <label for="eventSelector">Selector:</label>
            <input type="text" id="eventSelector" value="${event.selector || ''}" placeholder="CSS selector">
          </div>
        `;
      
      case 'input':
        return `
          <div class="form-group">
            <label for="eventSelector">Selector:</label>
            <input type="text" id="eventSelector" value="${event.selector || ''}" placeholder="CSS selector">
          </div>
          <div class="form-group">
            <label for="eventValue">Value:</label>
            <input type="text" id="eventValue" value="${event.value || ''}" placeholder="Input value">
          </div>
        `;
      
      case 'navigate':
        return `
          <div class="form-group">
            <label for="eventUrl">URL:</label>
            <input type="url" id="eventUrl" value="${event.url || ''}" placeholder="https://example.com">
          </div>
        `;
      
      case 'wait':
        return `
          <div class="form-group">
            <label for="eventDuration">Duration (ms):</label>
            <input type="number" id="eventDuration" value="${event.duration || 1000}" min="100" max="30000">
          </div>
        `;
      
      case 'text_search_click':
        return `
          <div class="form-group">
            <label for="eventSearchText">Search Text:</label>
            <input type="text" id="eventSearchText" value="${event.searchText || ''}" placeholder="Text to search for">
          </div>
        `;
      
      default:
        return '<p>No specific fields for this event type</p>';
    }
  }

  /**
   * Show event details in modal
   */
  showEventDetails(eventIndex) {
    if (!this.flowManager || !this.flowManager.currentFlow) return;

    const event = this.flowManager.currentFlow.flow_steps[eventIndex];
    if (!event) return;

    const detailsHTML = `
      <div class="event-details-view">
        <div class="detail-section">
          <h5>Basic Information</h5>
          <table class="details-table">
            <tr><td><strong>Type:</strong></td><td>${event.type}</td></tr>
            <tr><td><strong>Name:</strong></td><td>${event.name || 'Unnamed'}</td></tr>
            <tr><td><strong>Order:</strong></td><td>${event.order || eventIndex + 1}</td></tr>
          </table>
        </div>
        
        <div class="detail-section">
          <h5>Parameters</h5>
          <pre class="json-display">${JSON.stringify(event.parameters || event, null, 2)}</pre>
        </div>
        
        ${event.success_criteria ? `
          <div class="detail-section">
            <h5>Success Criteria</h5>
            <pre class="json-display">${JSON.stringify(event.success_criteria, null, 2)}</pre>
          </div>
        ` : ''}
      </div>
    `;

    if (this.flowManager && this.flowManager.showModal) {
      this.flowManager.showModal(`Event ${eventIndex + 1} Details`, detailsHTML);
    }
  }

  /**
   * Update flow displays in UI
   */
  async updateFlowDisplays() {
    try {
      if (this.flowManager && this.flowManager.currentFlow) {
        // Update enhanced flow list
        const enhancedFlowContainer = document.getElementById('flowEventsContainer');
        if (enhancedFlowContainer) {
          enhancedFlowContainer.innerHTML = this.generateEnhancedFlowHTML();
        }

        // Update traditional flow list
        await this.flowManager.updateFlowUI();

        // Save current flow to storage
        await chrome.storage.local.set({ 
          currentFlow: this.flowManager.currentFlow 
        });
      }
    } catch (error) {
      console.error('Error updating flow displays:', error);
    }
  }

  /**
   * Generate enhanced flow HTML
   */
  generateEnhancedFlowHTML() {
    if (!this.flowManager || !this.flowManager.currentFlow || !this.flowManager.currentFlow.flow_steps) {
      return `
        <p class="no-flow-message">No flow loaded. Loading from primary source...</p>
        <div class="flow-controls-enhanced">
          <button class="btn btn-primary add-event">
            ➕ Add Event
          </button>
        </div>
      `;
    }

    const flow = this.flowManager.currentFlow;
    
    return `
      <div class="flow-info">
        <h5>${flow.name || 'Untitled Flow'}</h5>
        <p class="flow-description">${flow.description || 'No description available'}</p>
        <div class="flow-stats">
          <span class="stat">📋 ${flow.flow_steps.length} Events</span>
          <span class="stat">📝 ${flow.version || 'v1.0.0'}</span>
        </div>
      </div>
      
      <div class="flow-events">
        ${flow.flow_steps.map((event, index) => this.generateEventCardHTML(event, index)).join('')}
      </div>

      <div class="flow-controls-enhanced">
        <button class="btn btn-success add-event">
          ➕ Add Event
        </button>
        <button class="btn btn-primary test-all-events">
          🧪 Test All Events
        </button>
        <button class="btn btn-success" id="executeFlow">
          🚀 Execute Flow
        </button>
      </div>
    `;
  }

  /**
   * Generate individual event card HTML
   */
  generateEventCardHTML(event, index) {
    return `
      <div class="event-card" data-index="${index}">
        <div class="event-header">
          <div class="event-type-badge ${this.getEventTypeClass(event.type)}">
            ${this.getEventIcon(event.type)} ${event.type}
          </div>
          <div class="event-status" id="eventStatus_${index}">
            <span class="status-dot" data-status="pending" title="Not tested"></span>
          </div>
        </div>
        
        <div class="event-content">
          <h6 class="event-name">${event.name || `Event ${index + 1}`}</h6>
          <p class="event-description">${event.description || 'No description'}</p>
          
          <div class="event-details">
            ${this.generateEventDetailsHTML(event)}
          </div>
        </div>
        
        <div class="event-actions">
          <button class="btn btn-sm btn-outline test-single-event" data-event-index="${index}" title="Test this event">
            🧪 Test
          </button>
          <button class="btn btn-sm btn-primary execute-single-event" data-event-index="${index}" title="Execute this event">
            ▶️ Run
          </button>
          <button class="btn btn-sm btn-outline edit-event" data-event-index="${index}" title="Edit event parameters">
            ✏️ Edit
          </button>
          <button class="btn btn-sm btn-warning delete-event" data-event-index="${index}" title="Delete this event">
            🗑️ Delete
          </button>
        </div>
      </div>
    `;
  }

  /**
   * Generate event details HTML
   */
  generateEventDetailsHTML(event) {
    const details = [];
    
    if (event.selector) {
      details.push(`<span class="detail-item"><strong>Selector:</strong> ${event.selector}</span>`);
    }
    if (event.value) {
      details.push(`<span class="detail-item"><strong>Value:</strong> ${event.value}</span>`);
    }
    if (event.url) {
      details.push(`<span class="detail-item"><strong>URL:</strong> ${event.url}</span>`);
    }
    if (event.duration) {
      details.push(`<span class="detail-item"><strong>Duration:</strong> ${event.duration}ms</span>`);
    }
    if (event.searchText || event.searchTexts) {
      const searchText = event.searchText || (event.searchTexts && event.searchTexts.join(', '));
      details.push(`<span class="detail-item"><strong>Search:</strong> ${searchText}</span>`);
    }
    
    return details.length > 0 ? details.join('') : '<span class="detail-item">No specific parameters</span>';
  }

  /**
   * Get event type CSS class
   */
  getEventTypeClass(type) {
    const typeClasses = {
      'click': 'event-type-click',
      'input': 'event-type-input',
      'navigate': 'event-type-navigate',
      'wait': 'event-type-wait',
      'text_search_click': 'event-type-search',
      'conditional_action': 'event-type-conditional',
      'extract': 'event-type-extract'
    };
    return typeClasses[type] || 'event-type-default';
  }

  /**
   * Get event type icon
   */
  getEventIcon(type) {
    const typeIcons = {
      'click': '👆',
      'input': '⌨️',
      'navigate': '🌐',
      'wait': '⏳',
      'text_search_click': '🔍',
      'conditional_action': '🔀',
      'extract': '📊',
      'scroll': '📜'
    };
    return typeIcons[type] || '⚙️';
  }

  /**
   * Handle test popup OK button detection and clicking
   */
  async handleTestPopupOkClick() {
    try {
      this.showLoading('Testing popup OK button click...');

      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      if (!tab) {
        throw new Error('No active tab found');
      }

      // Send detect and click popup OK test message
      const response = await chrome.tabs.sendMessage(tab.id, {
        action: 'detect_and_click_popup_ok',
        popupSelector: '.PopupBoxLogin',
        okButtonSelector: '#MainContent_btnOkay',
        timeout: 10000,
        waitAfterClick: 1000
      });

      if (response && response.success) {
        this.showToast('✅ Popup detection and OK button click test completed', 'success');
      } else {
        throw new Error(response?.error || 'Popup OK click test failed');
      }

    } catch (error) {
      console.error('❌ Popup OK click test failed:', error);
      this.showToast(`Popup test failed: ${error.message}`, 'error');
    } finally {
      this.hideLoading();
    }
  }
}

// Initialize popup when DOM is ready
let popupInterface;

document.addEventListener('DOMContentLoaded', () => {
  popupInterface = new PopupInterface();
  
  // Make it globally accessible for modal buttons
  window.popupInterface = popupInterface;
}); 