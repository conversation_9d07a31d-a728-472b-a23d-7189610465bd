<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Venus-Millware AutoFill</title>
    <link rel="stylesheet" href="styles/popup.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <img src="icons/icon32.png" alt="Venus-Millware AutoFill" class="logo">
            <h1>Venus-Millware AutoFill</h1>
            <div class="status-indicator" id="statusIndicator">
                <span class="status-dot"></span>
                <span class="status-text">Siap</span>
            </div>
        </div>

        <!-- Quick Run Section -->
        <div class="quick-run-section">
            <h3>🚀 Otomatisasi Timesheet</h3>
            <div class="quick-run-info">
                <div class="info-item">
                    <strong>Target:</strong> millwarep3.rebinmas.com:8003
                </div>
                <div class="info-item">
                    <strong>API:</strong> localhost:5173/api/staging/data
                </div>
            </div>
            <button class="btn btn-success btn-large btn-primary-action" id="runTimesheetAutomation" title="Jalankan otomatisasi timesheet lengkap: login + pemrosesan data">
                ⚡ Jalankan Otomatisasi
            </button>
            <button class="btn btn-primary btn-large" id="runPostLoginAutomation" title="Jalankan sequence post-login: OK click + navigasi + New click + text search">
                🔄 Post-Login Sequence
            </button>
            <small style="display: block; margin-top: 8px; color: #666; font-size: 12px;">
                Login otomatis ke sistem timesheet dan proses data staging
            </small>
        </div>
 
        <!-- Navigation Tabs -->
        <div class="nav-tabs">
            <button class="tab-button active" data-tab="config">Konfigurasi</button>
            <button class="tab-button" data-tab="data">Pratinjau Data</button>
            <button class="tab-button" data-tab="flow">Definisi Flow</button>
            <button class="tab-button" data-tab="textsearch">Text Search</button>
            <button class="tab-button" data-tab="execution">Eksekusi</button>
        </div>

        <!-- Configuration Tab -->
        <div class="tab-content active" id="config">
            <div class="section">
                <h3>Konfigurasi Website Target</h3>
                <div class="form-group">
                    <label for="targetUrl">Target Website URL:</label>
                    <input type="url" id="targetUrl" 
                           value="http://millwarep3.rebinmas.com:8003/"
                           placeholder="http://millwarep3.rebinmas.com:8003/">
                </div>
                <div class="form-group">
                    <label for="username">Username:</label>
                    <input type="text" id="username" 
                           value="adm075"
                           placeholder="adm075">
                </div>
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" 
                           value="adm075"
                           placeholder="Masukkan password">
                </div>
                <button class="btn btn-primary" id="saveConfig">Simpan Konfigurasi</button>
            </div>

            <div class="section">
                <h3>Konfigurasi Staging API</h3>
                <div class="form-group">
                    <label for="scriptUrl">API Base URL:</label>
                    <input type="url" id="scriptUrl" 
                           value="http://localhost:5173/api"
                           placeholder="http://localhost:5173/api">
                </div>
                <div class="form-group">
                    <label for="sheetName">API Key (Opsional):</label>
                    <input type="text" id="sheetName" placeholder="Masukkan API key jika diperlukan">
                </div>
                <div class="form-group">
                    <label for="delayInterval">Jeda Langkah (ms):</label>
                    <input type="number" id="delayInterval" 
                           value="1000" 
                           min="500" 
                           max="5000"
                           placeholder="1000">
                    <small>Jeda antara langkah otomatisasi (default: 1000ms)</small>
                </div>
                <button class="btn btn-secondary" id="testConnection">Test Koneksi</button>
                <button class="btn btn-secondary" id="debugConnection">Debug Koneksi</button>
            </div>

            <div class="section">
                <h3>Informasi Debug</h3>
                <div class="debug-info" id="debugInfo" style="display: none;">
                    <div class="form-group">
                        <label>Konfigurasi Saat Ini:</label>
                        <textarea id="debugOutput" readonly style="width: 100%; height: 120px; font-family: monospace; font-size: 11px; background: #f8f9fa; border: 1px solid #ddd; padding: 8px;"></textarea>
                    </div>
                    <button class="btn btn-secondary" id="copyDebugInfo">Salin Info Debug</button>
                </div>
            </div>
        </div>

        <!-- Data Preview Tab -->
        <div class="tab-content" id="data">
            <div class="section">
                <h3>Pratinjau Data Timesheet</h3>
                <div class="data-controls">
                    <button class="btn btn-primary" id="fetchData">Ambil Data Staging</button>
                    <button class="btn btn-secondary" id="refreshData">Refresh</button>
                </div>
                <div class="data-status" id="dataStatus">
                    <span>Tidak ada data timesheet yang dimuat</span>
                </div>
                <div class="data-preview" id="dataPreview">
                    <!-- Data will be populated here -->
                </div>
            </div>
        </div>

        <!-- Flow Definition Tab -->
        <div class="tab-content" id="flow">
            <div class="section">
                <h3>📋 Flow Management</h3>
                
                <!-- File Operations -->
                <div class="file-operations">
                    <h4>File Operations</h4>
                    <div class="file-controls">
                        <button class="btn btn-success" id="saveFlowToFile" title="Save current flow as JSON file to Downloads folder">
                            💾 Save to File
                        </button>
                        <button class="btn btn-primary" id="loadFlowFromFile" title="Load flow definition from JSON file">
                            📂 Load from File
                        </button>
                        <button class="btn btn-warning" id="deleteFlowFile" title="Delete flow from storage and Downloads folder">
                            🗑️ Delete Flow
                        </button>
                    </div>
                    <small class="help-text">Flow files are saved to your Downloads folder with naming: venus-flow-[name]-[timestamp].json</small>
                </div>

                <!-- Flow Validation -->
                <div class="validation-section">
                    <h4>Flow Validation & Testing</h4>
                    <div class="validation-controls">
                        <button class="btn btn-secondary" id="validateFlow" title="Validate flow structure and requirements">
                            ✅ Validate Flow
                        </button>
                        <button class="btn btn-secondary" id="dryRunFlow" title="Perform dry run simulation without executing">
                            🧪 Dry Run
                        </button>
                        <button class="btn btn-info" id="preflightCheck" title="Check if required elements exist on current page">
                            🔍 Preflight Check
                        </button>
                    </div>
                    <div class="validation-status" id="validationStatus">
                        <span class="status-text">No validation performed</span>
                    </div>
                </div>

                <!-- Legacy Controls -->
                <div class="legacy-controls">
                    <h4>Flow Definition</h4>
                    <div class="flow-controls">
                        <button class="btn btn-success add-event">➕ Tambah Event</button>
                        <button class="btn btn-primary" id="addEvent">Tambah Event</button>
                        <button class="btn btn-secondary" id="saveFlow">Simpan Flow</button>
                        <button class="btn btn-secondary" id="loadFlow">Muat Flow</button>
                        <button class="btn btn-warning" id="loadPredefinedFlow">Muat Flow Timesheet</button>
                    </div>
                </div>

                <!-- Enhanced Flow List -->
                <div class="enhanced-flow-list" id="enhancedFlowList">
                    <h4>Flow Events</h4>
                    <div class="flow-events-container" id="flowEventsContainer">
                        <!-- Enhanced flow events will be listed here -->
                    </div>
                </div>

                <!-- Traditional Flow List (Legacy) -->
                <div class="flow-list" id="flowList">
                    <!-- Traditional flow events will be listed here -->
                </div>
                
                <div class="flow-quick-actions" style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #e0e0e0;">
                    <h4 style="margin-bottom: 10px; font-size: 14px; color: #333;">Aksi Cepat</h4>
                    <button class="btn btn-success btn-large" id="startFlow" title="Eksekusi alur otomatisasi yang telah didefinisikan dengan navigasi otomatis">
                        🚀 Mulai Alur Otomatisasi
                    </button>
                    <small style="display: block; margin-top: 8px; color: #666; font-size: 12px;">
                        Ini akan secara otomatis navigasi ke URL target dan menjalankan urutan otomatisasi yang telah didefinisikan.
                    </small>
                </div>
            </div>
        </div>

        <!-- Text Search Tab -->
        <div class="tab-content" id="textsearch">
            <div class="section">
                <h3>🔍 Text Search Functionality</h3>
                <div class="text-search-info">
                    <p>Advanced text search similar to browser's Ctrl+F feature with highlighting and navigation.</p>
                </div>

                <div class="form-group">
                    <label for="searchText">Search Text:</label>
                    <input type="text" id="searchText" placeholder="Enter text to search..." style="width: 100%;">
                </div>

                <div class="search-options">
                    <label class="checkbox-label">
                        <input type="checkbox" id="caseSensitive"> Case Sensitive
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox" id="highlightMatches" checked> Highlight Matches
                    </label>
                </div>

                <div class="search-controls">
                    <button class="btn btn-primary" id="performSearch">🔍 Search</button>
                    <button class="btn btn-secondary" id="clearSearch">Clear</button>
                    <button class="btn btn-secondary" id="showSearchUI">Show Search UI</button>
                </div>

                <div class="search-navigation" id="searchNavigation" style="display: none;">
                    <div class="search-results-info" id="searchResultsInfo">
                        No matches found
                    </div>
                    <div class="navigation-controls">
                        <button class="btn btn-sm" id="previousMatch" title="Previous match (Shift+F3)">↑ Prev</button>
                        <button class="btn btn-sm" id="nextMatch" title="Next match (F3)">↓ Next</button>
                    </div>
                </div>

                <div class="keyboard-shortcuts">
                    <h4>Keyboard Shortcuts:</h4>
                    <ul>
                        <li><strong>Ctrl+F:</strong> Open text search UI</li>
                        <li><strong>F3:</strong> Next match</li>
                        <li><strong>Shift+F3:</strong> Previous match</li>
                        <li><strong>Escape:</strong> Close search UI</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Execution Tab -->
        <div class="tab-content" id="execution">
            <div class="section">
                <h3>Kontrol Eksekusi Otomatisasi</h3>
                <div class="execution-controls">
                    <button class="btn btn-success" id="runExecution">Jalankan Otomatisasi</button>
                    <button class="btn btn-warning" id="pauseExecution">Jeda</button>
                    <button class="btn btn-danger" id="stopExecution">Berhenti</button>
                </div>
                <div class="execution-status" id="executionStatus">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="status-text" id="executionStatusText">Siap untuk menjalankan otomatisasi</div>
                </div>
                <div class="execution-log" id="executionLog">
                    <!-- Execution logs will appear here -->
                </div>
            </div>
        </div>
    </div>

    <script src="scripts/api-service.js"></script>
    <script src="utils/flow-manager.js"></script>
    <script src="scripts/popup.js"></script>
    <script>
        // Debug initialization
        console.log('Popup HTML loaded, initializing extension...');
        
        // Ensure all dependencies are available
        window.addEventListener('load', () => {
            console.log('Window loaded, checking dependencies...');
            
            if (typeof AutomationBotPopup === 'undefined') {
                console.error('AutomationBotPopup class not found!');
            } else {
                console.log('AutomationBotPopup class available');
            }
            
            if (typeof ApiService === 'undefined') {
                console.warn('ApiService class not found');
            }
            
            if (typeof FlowManager === 'undefined') {
                console.warn('FlowManager class not found, will use fallback methods');
            }
        });
    </script>
</body>
</html>
