{"flow_id": "millware_login_automation_v2", "name": "Enhanced Millware Login with <PERSON><PERSON> Handling", "description": "Automated login flow with robust popup dialog handling for Millware timesheet system", "version": "2.0.0", "author": "Atha Rizki Pangestu - IT Rebinmas (Delloyd Group)", "created_date": "2024-01-20", "target_system": {"name": "Millware ERP System", "base_url": "http://millwarep3.rebinmas.com:8003/", "authentication_required": true}, "flow_metadata": {"estimated_duration": "15-30 seconds", "complexity": "medium", "success_rate": "high", "dependencies": ["active_internet", "target_server_accessible"]}, "visual_flow_config": {"layout": "vertical", "theme": "modern", "animation_enabled": true, "highlight_current_step": true, "highlight_color": "#ff4444", "success_color": "#22c55e", "error_color": "#ef4444", "pending_color": "#3b82f6"}, "execution_settings": {"timeout_per_step": 10000, "retry_failed_steps": true, "continue_on_error": false, "take_screenshots": true, "log_detailed_errors": true}, "flow_steps": [{"id": "wait_initial", "type": "wait", "name": "Initial Page Load Wait", "description": "Wait for login page to fully load", "parameters": {"duration": 500}}, {"id": "input_username", "type": "input", "name": "<PERSON><PERSON> Username", "description": "Input username into login field", "parameters": {"selector": "#txtUsername", "value": "adm075", "clear_first": true, "simulate_typing": true, "typing_delay": 50}}, {"id": "wait_after_username", "type": "wait", "name": "Wait After Username", "description": "Brief wait after username input", "parameters": {"duration": 300}}, {"id": "input_password", "type": "input", "name": "Enter Password", "description": "Input password into password field", "parameters": {"selector": "#txtPassword", "value": "adm075", "clear_first": true, "simulate_typing": true, "typing_delay": 50}}, {"id": "wait_after_password", "type": "wait", "name": "Wait After Password", "description": "Brief wait after password input", "parameters": {"duration": 300}}, {"id": "click_login", "type": "click", "name": "<PERSON><PERSON> Login <PERSON>", "description": "Click the login button to authenticate", "parameters": {"selector": "#btnLogin", "wait_after_click": 1000}}, {"id": "handle_popup", "type": "popup_handler", "name": "<PERSON><PERSON>", "description": "Detect and handle the popup dialog that appears after login", "parameters": {"timeout": 10000, "popupSelectors": [".PopupBoxLogin", "[class*='popup']", "[class*='modal']"], "okButtonSelectors": ["#MainContent_btnOkay", ".button", "input[type='button'][value*='OK']", "input[type='button'][value*='Ok']"], "popupStabilizeDelay": 1000, "dismissalTimeout": 5000}}, {"id": "wait_popup_stability", "type": "wait_for_page_stability", "name": "Wait for Page Stability", "description": "Ensure page is stable after popup dismissal", "parameters": {"timeout": 3000, "maxWait": 10000}}, {"id": "navigate_to_tasks", "type": "navigate", "name": "Navigate to Task Register", "description": "Navigate to the Task Register List page", "parameters": {"url": "http://millwarep3.rebinmas.com:8003/en/PR/trx/frmPrTrxTaskRegisterList.aspx", "waitForPopupDismissal": true, "popupDismissalDelay": 500, "ensurePageReady": true, "stabilityDelay": 1000, "timeout": 30000}}, {"id": "final_verification", "type": "wait", "name": "Final Verification Wait", "description": "Final wait to ensure automation completion", "parameters": {"duration": 1000}}], "flow_connections": [{"from": "wait_initial", "to": "input_username"}, {"from": "input_username", "to": "wait_after_username"}, {"from": "input_username", "to": "input_password"}, {"from": "input_password", "to": "wait_after_password"}, {"from": "wait_after_password", "to": "click_login"}, {"from": "click_login", "to": "handle_popup"}, {"from": "handle_popup", "to": "wait_popup_stability"}, {"from": "wait_popup_stability", "to": "navigate_to_tasks"}, {"from": "navigate_to_tasks", "to": "final_verification"}], "error_recovery": {"global_timeout": 120000, "screenshot_on_error": true, "retry_entire_flow": false, "recovery_actions": [{"error_type": "element_not_found", "action": "wait_and_retry", "max_retries": 2}, {"error_type": "navigation_timeout", "action": "refresh_and_retry", "max_retries": 1}, {"error_type": "authentication_failed", "action": "alert_user", "stop_execution": true}]}, "success_metrics": {"completion_time": "target_under_30s", "success_rate": "target_above_95%", "user_satisfaction": "high"}, "metadata": {"created": "2024-01-01", "author": "Venus AutoFill Enhanced", "target_url": "http://millwarep3.rebinmas.com:8003/", "requirements": ["Valid credentials (adm075/adm075)", "Stable internet connection", "Popup dialog handling enabled"], "notes": ["Enhanced flow with popup dialog detection and handling", "Includes page stability checks and enhanced error handling", "Robust click and navigation methods for better reliability"]}}